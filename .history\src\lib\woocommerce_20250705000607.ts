import { gql } from 'graphql-request';

// Check if we're on the client side
const isClient = typeof window !== 'undefined';

// Product query using fragments for different product types
export const GET_PRODUCTS = gql`
  query GetProducts($first: Int, $after: String, $where: RootQueryToProductConnectionWhereArgs) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        price
        regularPrice
        salePrice
        onSale
        image {
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            sourceUrl
            altText
          }
        }
        ... on SimpleProduct {
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          stockStatus
          stockQuantity
          productCategories {
            nodes {
              name
              slug
            }
          }
        }
      }
    }
  }
`;

// Get a single product with variations if it's a variable product
export const GET_PRODUCT = gql`
  query GetProduct($id: ID!) {
    product(id: $id, idType: SLUG) {
      id
      databaseId
      name
      slug
      description
      shortDescription
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      image {
        sourceUrl
        altText
      }
      galleryImages {
        nodes {
          sourceUrl
          altText
        }
      }
      ... on SimpleProduct {
        stockStatus
        stockQuantity
      }
      ... on VariableProduct {
        attributes {
          nodes {
            name
            options
            variation
          }
        }
        defaultAttributes {
          nodes {
            name
            value
          }
        }
        variations {
          nodes {
            id
            databaseId
            name
            price
            regularPrice
            salePrice
            stockStatus
            stockQuantity
            attributes {
              nodes {
                name
                value
              }
            }
          }
        }
      }
    }
  }
`;

// Cart query without the totalShipping field that's causing errors
export const GET_CART = gql`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              name
              slug
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`;

// Add to cart mutation
export const ADD_TO_CART = gql`
  mutation AddToCart($input: AddToCartInput!) {
    addToCart(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
      }
    }
  }
`;

// Update cart item quantities
export const UPDATE_CART_ITEM = gql`
  mutation UpdateCartItems($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                name
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
      }
    }
  }
`;

// Remove items from cart
export const REMOVE_ITEMS_FROM_CART = gql`
  mutation RemoveItemsFromCart($input: RemoveItemsFromCartInput!) {
    removeItemsFromCart(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                name
              }
            }
            quantity
          }
        }
        subtotal
        total
        isEmpty
      }
    }
  }
`;

/**
 * Fetch data from WooCommerce GraphQL API with caching and revalidation
 */
export async function fetchFromWooCommerce<T = Record<string, any>>(
  query: string,
  variables = {},
  tags: string[] = [],
  revalidate = 60
): Promise<T> {
  try {
    // Use different approaches for client and server
    if (isClient) {
      // When on client, use our proxy API route to avoid CORS issues
      const proxyEndpoint = '/api/graphql';

      // Build the fetch options
      const fetchOptions: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, variables }),
      };

      const response = await fetch(proxyEndpoint, fetchOptions);

      if (!response.ok) {
        throw new Error(`GraphQL API responded with status ${response.status}`);
      }

      const data = await response.json();

      if (data.errors) {
        console.error('GraphQL Errors:', data.errors);
        throw new Error(`GraphQL Error: ${data.errors[0]?.message || 'Unknown error'}`);
      }

      return data.data;
    } else {
      // Server-side: use direct GraphQL endpoint
      const endpoint = process.env.NEXT_PUBLIC_WORDPRESS_URL
        ? `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/graphql`
        : '';

      if (!endpoint) {
        throw new Error('WordPress GraphQL endpoint not configured');
      }

      const fetchOptions: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, variables }),
        next: {
          revalidate,
          tags
        },
      };

      const response = await fetch(endpoint, fetchOptions);

      if (!response.ok) {
        throw new Error(`GraphQL API responded with status ${response.status}`);
      }

      const data = await response.json();

      if (data.errors) {
        console.error('GraphQL Errors:', data.errors);
        throw new Error(`GraphQL Error: ${data.errors[0]?.message || 'Unknown error'}`);
      }

      return data.data;
    }
  } catch (error) {
    console.error('Error fetching from WooCommerce:', error);
    throw error;
  }
}

/**
 * GraphQL fetch with retry logic
 */
export async function wooGraphQLFetch<T = Record<string, any>>(
  query: string,
  variables = {},
  retries = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fetchFromWooCommerce<T>(query, variables);
    } catch (error) {
      lastError = error as Error;
      console.error(`GraphQL request failed (attempt ${attempt}/${retries}):`, error);

      if (attempt < retries) {
        console.log(`Retrying request (${attempt}/${retries}) after ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }
  }

  console.error(`Failed after ${retries} attempts:`, lastError);
  throw lastError;
}

/**
 * Add items to cart - Updated for current WooGraphQL schema
 */
export async function addToCart(cartId: string, items: any[]) {
  try {
    interface AddToCartResponse {
      addToCart: {
        cart: {
          contents: {
            nodes: Array<{
              key: string;
              product: {
                node: {
                  id: string;
                  databaseId: number;
                  name: string;
                  slug: string;
                  type: string;
                  image: {
                    sourceUrl: string;
                    altText: string;
                  };
                };
              };
              quantity: number;
              total: string;
            }>;
          };
          subtotal: string;
          total: string;
          totalTax: string;
          isEmpty: boolean;
        };
      };
    }

    // Convert base64 encoded productId to integer databaseId
    const processedItems = items.map(item => {
      let productId = item.productId;

      // If productId is a base64 encoded string, try to extract the database ID
      if (typeof productId === 'string' && !productId.match(/^\d+$/)) {
        // For base64 encoded IDs like "cG9zdDo0Mg==", we need the databaseId
        // This should be passed from the frontend instead
        console.warn('Received base64 encoded productId:', productId, 'This should be the databaseId integer instead');
        throw new Error('Invalid productId format. Expected integer databaseId, got base64 encoded string.');
      }

      // Ensure productId is an integer
      const databaseId = typeof productId === 'string' ? parseInt(productId, 10) : productId;

      if (isNaN(databaseId)) {
        throw new Error(`Invalid productId: ${productId}. Must be a valid integer.`);
      }

      return {
        productId: databaseId,
        quantity: item.quantity,
        variationId: item.variationId || undefined
      };
    });

    const input = {
      clientMutationId: `add_to_cart_${Date.now()}`,
      productId: processedItems[0].productId,
      quantity: processedItems[0].quantity,
      variationId: processedItems[0].variationId
    };

    console.log('Adding to cart with input:', input);

    const response = await wooGraphQLFetch<AddToCartResponse>(
      ADD_TO_CART.loc?.source?.body || ADD_TO_CART.toString(),
      { input }
    );

    console.log('Add to cart response:', response);
    return response.addToCart.cart;
  } catch (error) {
    console.error('Error adding items to cart:', error);
    throw error;
  }
}