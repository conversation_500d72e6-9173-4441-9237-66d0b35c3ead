/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-_"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false!":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=false! ***!
  \*******************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {



/***/ }),

/***/ "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/class-variance-authority/dist/index.mjs ***!
  \**************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cva: function() { return /* binding */ cva; },\n/* harmony export */   cx: function() { return /* binding */ cx; }\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ \nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nconst cx = clsx__WEBPACK_IMPORTED_MODULE_0__.clsx;\nconst cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: function() { return /* binding */ clsx; }\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ __webpack_exports__[\"default\"] = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGNBQWMsYUFBYSwrQ0FBK0MsZ0RBQWdELGVBQWUsUUFBUSxJQUFJLDBDQUEwQyx5Q0FBeUMsU0FBZ0IsZ0JBQWdCLHdDQUF3QyxJQUFJLG1EQUFtRCxTQUFTLCtEQUFlLElBQUkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2Nsc3gvZGlzdC9jbHN4Lm1qcz9iZDAzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHIoZSl7dmFyIHQsZixuPVwiXCI7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGV8fFwibnVtYmVyXCI9PXR5cGVvZiBlKW4rPWU7ZWxzZSBpZihcIm9iamVjdFwiPT10eXBlb2YgZSlpZihBcnJheS5pc0FycmF5KGUpKXt2YXIgbz1lLmxlbmd0aDtmb3IodD0wO3Q8bzt0KyspZVt0XSYmKGY9cihlW3RdKSkmJihuJiYobis9XCIgXCIpLG4rPWYpfWVsc2UgZm9yKGYgaW4gZSllW2ZdJiYobiYmKG4rPVwiIFwiKSxuKz1mKTtyZXR1cm4gbn1leHBvcnQgZnVuY3Rpb24gY2xzeCgpe2Zvcih2YXIgZSx0LGY9MCxuPVwiXCIsbz1hcmd1bWVudHMubGVuZ3RoO2Y8bztmKyspKGU9YXJndW1lbnRzW2ZdKSYmKHQ9cihlKSkmJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBkZWZhdWx0IGNsc3g7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/instant.mjs":
/*!****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/instant.mjs ***!
  \****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createInstantAnimation: function() { return /* binding */ createInstantAnimation; }\n/* harmony export */ });\n/* harmony import */ var _js_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./js/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n\n\n\nfunction createInstantAnimation({ keyframes, delay, onUpdate, onComplete, }) {\n    const setValue = () => {\n        onUpdate && onUpdate(keyframes[keyframes.length - 1]);\n        onComplete && onComplete();\n        /**\n         * TODO: As this API grows it could make sense to always return\n         * animateValue. This will be a bigger project as animateValue\n         * is frame-locked whereas this function resolves instantly.\n         * This is a behavioural change and also has ramifications regarding\n         * assumptions within tests.\n         */\n        return {\n            time: 0,\n            speed: 1,\n            duration: 0,\n            play: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            pause: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            stop: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            then: (resolve) => {\n                resolve();\n                return Promise.resolve();\n            },\n            cancel: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n            complete: (_utils_noop_mjs__WEBPACK_IMPORTED_MODULE_0__.noop),\n        };\n    };\n    return delay\n        ? (0,_js_index_mjs__WEBPACK_IMPORTED_MODULE_1__.animateValue)({\n            keyframes: [0, 1],\n            duration: 0,\n            delay,\n            onComplete: setValue,\n        })\n        : setValue();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/instant.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs":
/*!****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   frameloopDriver: function() { return /* binding */ frameloopDriver; }\n/* harmony export */ });\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\nconst frameloopDriver = (update) => {\n    const passTimestamp = ({ timestamp }) => update(timestamp);\n    return {\n        start: () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frame.update(passTimestamp, true),\n        stop: () => (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.cancelFrame)(passTimestamp),\n        /**\n         * If we're processing this frame we can use the\n         * framelocked timestamp to keep things in sync.\n         */\n        now: () => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.isProcessing ? _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_0__.frameData.timestamp : performance.now(),\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy9qcy9kcml2ZXItZnJhbWVsb29wLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE2RTs7QUFFN0U7QUFDQSw2QkFBNkIsV0FBVztBQUN4QztBQUNBLHFCQUFxQix1REFBSztBQUMxQixvQkFBb0IsaUVBQVc7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsMkRBQVMsZ0JBQWdCLDJEQUFTO0FBQ3JEO0FBQ0E7O0FBRTJCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy9qcy9kcml2ZXItZnJhbWVsb29wLm1qcz80OTI0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZyYW1lLCBjYW5jZWxGcmFtZSwgZnJhbWVEYXRhIH0gZnJvbSAnLi4vLi4vLi4vZnJhbWVsb29wL2ZyYW1lLm1qcyc7XG5cbmNvbnN0IGZyYW1lbG9vcERyaXZlciA9ICh1cGRhdGUpID0+IHtcbiAgICBjb25zdCBwYXNzVGltZXN0YW1wID0gKHsgdGltZXN0YW1wIH0pID0+IHVwZGF0ZSh0aW1lc3RhbXApO1xuICAgIHJldHVybiB7XG4gICAgICAgIHN0YXJ0OiAoKSA9PiBmcmFtZS51cGRhdGUocGFzc1RpbWVzdGFtcCwgdHJ1ZSksXG4gICAgICAgIHN0b3A6ICgpID0+IGNhbmNlbEZyYW1lKHBhc3NUaW1lc3RhbXApLFxuICAgICAgICAvKipcbiAgICAgICAgICogSWYgd2UncmUgcHJvY2Vzc2luZyB0aGlzIGZyYW1lIHdlIGNhbiB1c2UgdGhlXG4gICAgICAgICAqIGZyYW1lbG9ja2VkIHRpbWVzdGFtcCB0byBrZWVwIHRoaW5ncyBpbiBzeW5jLlxuICAgICAgICAgKi9cbiAgICAgICAgbm93OiAoKSA9PiBmcmFtZURhdGEuaXNQcm9jZXNzaW5nID8gZnJhbWVEYXRhLnRpbWVzdGFtcCA6IHBlcmZvcm1hbmNlLm5vdygpLFxuICAgIH07XG59O1xuXG5leHBvcnQgeyBmcmFtZWxvb3BEcml2ZXIgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs":
/*!*****************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateValue: function() { return /* binding */ animateValue; }\n/* harmony export */ });\n/* harmony import */ var _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../generators/keyframes.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/keyframes.mjs\");\n/* harmony import */ var _generators_spring_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../generators/spring/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/spring/index.mjs\");\n/* harmony import */ var _generators_inertia_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../generators/inertia.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/inertia.mjs\");\n/* harmony import */ var _driver_frameloop_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./driver-frameloop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/driver-frameloop.mjs\");\n/* harmony import */ var _utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../utils/interpolate.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/interpolate.mjs\");\n/* harmony import */ var _utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/clamp.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/clamp.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../generators/utils/calc-duration.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/generators/utils/calc-duration.mjs\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\n\n\n\n\n\n\n\n\nconst types = {\n    decay: _generators_inertia_mjs__WEBPACK_IMPORTED_MODULE_0__.inertia,\n    inertia: _generators_inertia_mjs__WEBPACK_IMPORTED_MODULE_0__.inertia,\n    tween: _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes,\n    keyframes: _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes,\n    spring: _generators_spring_index_mjs__WEBPACK_IMPORTED_MODULE_2__.spring,\n};\n/**\n * Animate a single value on the main thread.\n *\n * This function is written, where functionality overlaps,\n * to be largely spec-compliant with WAAPI to allow fungibility\n * between the two.\n */\nfunction animateValue({ autoplay = true, delay = 0, driver = _driver_frameloop_mjs__WEBPACK_IMPORTED_MODULE_3__.frameloopDriver, keyframes: keyframes$1, type = \"keyframes\", repeat = 0, repeatDelay = 0, repeatType = \"loop\", onPlay, onStop, onComplete, onUpdate, ...options }) {\n    let speed = 1;\n    let hasStopped = false;\n    let resolveFinishedPromise;\n    let currentFinishedPromise;\n    /**\n     * Resolve the current Promise every time we enter the\n     * finished state. This is WAAPI-compatible behaviour.\n     */\n    const updateFinishedPromise = () => {\n        currentFinishedPromise = new Promise((resolve) => {\n            resolveFinishedPromise = resolve;\n        });\n    };\n    // Create the first finished promise\n    updateFinishedPromise();\n    let animationDriver;\n    const generatorFactory = types[type] || _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes;\n    /**\n     * If this isn't the keyframes generator and we've been provided\n     * strings as keyframes, we need to interpolate these.\n     */\n    let mapNumbersToKeyframes;\n    if (generatorFactory !== _generators_keyframes_mjs__WEBPACK_IMPORTED_MODULE_1__.keyframes &&\n        typeof keyframes$1[0] !== \"number\") {\n        if (true) {\n            (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_4__.invariant)(keyframes$1.length === 2, `Only two keyframes currently supported with spring and inertia animations. Trying to animate ${keyframes$1}`);\n        }\n        mapNumbersToKeyframes = (0,_utils_interpolate_mjs__WEBPACK_IMPORTED_MODULE_5__.interpolate)([0, 100], keyframes$1, {\n            clamp: false,\n        });\n        keyframes$1 = [0, 100];\n    }\n    const generator = generatorFactory({ ...options, keyframes: keyframes$1 });\n    let mirroredGenerator;\n    if (repeatType === \"mirror\") {\n        mirroredGenerator = generatorFactory({\n            ...options,\n            keyframes: [...keyframes$1].reverse(),\n            velocity: -(options.velocity || 0),\n        });\n    }\n    let playState = \"idle\";\n    let holdTime = null;\n    let startTime = null;\n    let cancelTime = null;\n    /**\n     * If duration is undefined and we have repeat options,\n     * we need to calculate a duration from the generator.\n     *\n     * We set it to the generator itself to cache the duration.\n     * Any timeline resolver will need to have already precalculated\n     * the duration by this step.\n     */\n    if (generator.calculatedDuration === null && repeat) {\n        generator.calculatedDuration = (0,_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_6__.calcGeneratorDuration)(generator);\n    }\n    const { calculatedDuration } = generator;\n    let resolvedDuration = Infinity;\n    let totalDuration = Infinity;\n    if (calculatedDuration !== null) {\n        resolvedDuration = calculatedDuration + repeatDelay;\n        totalDuration = resolvedDuration * (repeat + 1) - repeatDelay;\n    }\n    let currentTime = 0;\n    const tick = (timestamp) => {\n        if (startTime === null)\n            return;\n        /**\n         * requestAnimationFrame timestamps can come through as lower than\n         * the startTime as set by performance.now(). Here we prevent this,\n         * though in the future it could be possible to make setting startTime\n         * a pending operation that gets resolved here.\n         */\n        if (speed > 0)\n            startTime = Math.min(startTime, timestamp);\n        if (speed < 0)\n            startTime = Math.min(timestamp - totalDuration / speed, startTime);\n        if (holdTime !== null) {\n            currentTime = holdTime;\n        }\n        else {\n            // Rounding the time because floating point arithmetic is not always accurate, e.g. 3000.367 - 1000.367 =\n            // 2000.0000000000002. This is a problem when we are comparing the currentTime with the duration, for\n            // example.\n            currentTime = Math.round(timestamp - startTime) * speed;\n        }\n        // Rebase on delay\n        const timeWithoutDelay = currentTime - delay * (speed >= 0 ? 1 : -1);\n        const isInDelayPhase = speed >= 0 ? timeWithoutDelay < 0 : timeWithoutDelay > totalDuration;\n        currentTime = Math.max(timeWithoutDelay, 0);\n        /**\n         * If this animation has finished, set the current time\n         * to the total duration.\n         */\n        if (playState === \"finished\" && holdTime === null) {\n            currentTime = totalDuration;\n        }\n        let elapsed = currentTime;\n        let frameGenerator = generator;\n        if (repeat) {\n            /**\n             * Get the current progress (0-1) of the animation. If t is >\n             * than duration we'll get values like 2.5 (midway through the\n             * third iteration)\n             */\n            const progress = Math.min(currentTime, totalDuration) / resolvedDuration;\n            /**\n             * Get the current iteration (0 indexed). For instance the floor of\n             * 2.5 is 2.\n             */\n            let currentIteration = Math.floor(progress);\n            /**\n             * Get the current progress of the iteration by taking the remainder\n             * so 2.5 is 0.5 through iteration 2\n             */\n            let iterationProgress = progress % 1.0;\n            /**\n             * If iteration progress is 1 we count that as the end\n             * of the previous iteration.\n             */\n            if (!iterationProgress && progress >= 1) {\n                iterationProgress = 1;\n            }\n            iterationProgress === 1 && currentIteration--;\n            currentIteration = Math.min(currentIteration, repeat + 1);\n            /**\n             * Reverse progress if we're not running in \"normal\" direction\n             */\n            const isOddIteration = Boolean(currentIteration % 2);\n            if (isOddIteration) {\n                if (repeatType === \"reverse\") {\n                    iterationProgress = 1 - iterationProgress;\n                    if (repeatDelay) {\n                        iterationProgress -= repeatDelay / resolvedDuration;\n                    }\n                }\n                else if (repeatType === \"mirror\") {\n                    frameGenerator = mirroredGenerator;\n                }\n            }\n            elapsed = (0,_utils_clamp_mjs__WEBPACK_IMPORTED_MODULE_7__.clamp)(0, 1, iterationProgress) * resolvedDuration;\n        }\n        /**\n         * If we're in negative time, set state as the initial keyframe.\n         * This prevents delay: x, duration: 0 animations from finishing\n         * instantly.\n         */\n        const state = isInDelayPhase\n            ? { done: false, value: keyframes$1[0] }\n            : frameGenerator.next(elapsed);\n        if (mapNumbersToKeyframes) {\n            state.value = mapNumbersToKeyframes(state.value);\n        }\n        let { done } = state;\n        if (!isInDelayPhase && calculatedDuration !== null) {\n            done = speed >= 0 ? currentTime >= totalDuration : currentTime <= 0;\n        }\n        const isAnimationFinished = holdTime === null &&\n            (playState === \"finished\" || (playState === \"running\" && done));\n        if (onUpdate) {\n            onUpdate(state.value);\n        }\n        if (isAnimationFinished) {\n            finish();\n        }\n        return state;\n    };\n    const stopAnimationDriver = () => {\n        animationDriver && animationDriver.stop();\n        animationDriver = undefined;\n    };\n    const cancel = () => {\n        playState = \"idle\";\n        stopAnimationDriver();\n        resolveFinishedPromise();\n        updateFinishedPromise();\n        startTime = cancelTime = null;\n    };\n    const finish = () => {\n        playState = \"finished\";\n        onComplete && onComplete();\n        stopAnimationDriver();\n        resolveFinishedPromise();\n    };\n    const play = () => {\n        if (hasStopped)\n            return;\n        if (!animationDriver)\n            animationDriver = driver(tick);\n        const now = animationDriver.now();\n        onPlay && onPlay();\n        if (holdTime !== null) {\n            startTime = now - holdTime;\n        }\n        else if (!startTime || playState === \"finished\") {\n            startTime = now;\n        }\n        if (playState === \"finished\") {\n            updateFinishedPromise();\n        }\n        cancelTime = startTime;\n        holdTime = null;\n        /**\n         * Set playState to running only after we've used it in\n         * the previous logic.\n         */\n        playState = \"running\";\n        animationDriver.start();\n    };\n    if (autoplay) {\n        play();\n    }\n    const controls = {\n        then(resolve, reject) {\n            return currentFinishedPromise.then(resolve, reject);\n        },\n        get time() {\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.millisecondsToSeconds)(currentTime);\n        },\n        set time(newTime) {\n            newTime = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.secondsToMilliseconds)(newTime);\n            currentTime = newTime;\n            if (holdTime !== null || !animationDriver || speed === 0) {\n                holdTime = newTime;\n            }\n            else {\n                startTime = animationDriver.now() - newTime / speed;\n            }\n        },\n        get duration() {\n            const duration = generator.calculatedDuration === null\n                ? (0,_generators_utils_calc_duration_mjs__WEBPACK_IMPORTED_MODULE_6__.calcGeneratorDuration)(generator)\n                : generator.calculatedDuration;\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.millisecondsToSeconds)(duration);\n        },\n        get speed() {\n            return speed;\n        },\n        set speed(newSpeed) {\n            if (newSpeed === speed || !animationDriver)\n                return;\n            speed = newSpeed;\n            controls.time = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_8__.millisecondsToSeconds)(currentTime);\n        },\n        get state() {\n            return playState;\n        },\n        play,\n        pause: () => {\n            playState = \"paused\";\n            holdTime = currentTime;\n        },\n        stop: () => {\n            hasStopped = true;\n            if (playState === \"idle\")\n                return;\n            playState = \"idle\";\n            onStop && onStop();\n            cancel();\n        },\n        cancel: () => {\n            if (cancelTime !== null)\n                tick(cancelTime);\n            cancel();\n        },\n        complete: () => {\n            playState = \"finished\";\n        },\n        sample: (elapsed) => {\n            startTime = 0;\n            return tick(elapsed);\n        },\n    };\n    return controls;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs ***!
  \*******************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAcceleratedAnimation: function() { return /* binding */ createAcceleratedAnimation; }\n/* harmony export */ });\n/* harmony import */ var _index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs\");\n/* harmony import */ var _easing_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./easing.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs\");\n/* harmony import */ var _utils_get_final_keyframe_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/get-final-keyframe.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs\");\n/* harmony import */ var _js_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../js/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/js/index.mjs\");\n/* harmony import */ var _utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/time-conversion.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/time-conversion.mjs\");\n/* harmony import */ var _utils_memo_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../utils/memo.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/memo.mjs\");\n/* harmony import */ var _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../utils/noop.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/noop.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\n\n\n\n\n\nconst supportsWaapi = (0,_utils_memo_mjs__WEBPACK_IMPORTED_MODULE_0__.memo)(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    \"backgroundColor\",\n]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\nconst requiresPregeneratedKeyframes = (valueName, options) => options.type === \"spring\" ||\n    valueName === \"backgroundColor\" ||\n    !(0,_easing_mjs__WEBPACK_IMPORTED_MODULE_1__.isWaapiSupportedEasing)(options.ease);\nfunction createAcceleratedAnimation(value, valueName, { onUpdate, onComplete, ...options }) {\n    const canAccelerateAnimation = supportsWaapi() &&\n        acceleratedValues.has(valueName) &&\n        !options.repeatDelay &&\n        options.repeatType !== \"mirror\" &&\n        options.damping !== 0 &&\n        options.type !== \"inertia\";\n    if (!canAccelerateAnimation)\n        return false;\n    /**\n     * TODO: Unify with js/index\n     */\n    let hasStopped = false;\n    let resolveFinishedPromise;\n    let currentFinishedPromise;\n    /**\n     * Cancelling an animation will write to the DOM. For safety we want to defer\n     * this until the next `update` frame lifecycle. This flag tracks whether we\n     * have a pending cancel, if so we shouldn't allow animations to finish.\n     */\n    let pendingCancel = false;\n    /**\n     * Resolve the current Promise every time we enter the\n     * finished state. This is WAAPI-compatible behaviour.\n     */\n    const updateFinishedPromise = () => {\n        currentFinishedPromise = new Promise((resolve) => {\n            resolveFinishedPromise = resolve;\n        });\n    };\n    // Create the first finished promise\n    updateFinishedPromise();\n    let { keyframes, duration = 300, ease, times } = options;\n    /**\n     * If this animation needs pre-generated keyframes then generate.\n     */\n    if (requiresPregeneratedKeyframes(valueName, options)) {\n        const sampleAnimation = (0,_js_index_mjs__WEBPACK_IMPORTED_MODULE_2__.animateValue)({\n            ...options,\n            repeat: 0,\n            delay: 0,\n        });\n        let state = { done: false, value: keyframes[0] };\n        const pregeneratedKeyframes = [];\n        /**\n         * Bail after 20 seconds of pre-generated keyframes as it's likely\n         * we're heading for an infinite loop.\n         */\n        let t = 0;\n        while (!state.done && t < maxDuration) {\n            state = sampleAnimation.sample(t);\n            pregeneratedKeyframes.push(state.value);\n            t += sampleDelta;\n        }\n        times = undefined;\n        keyframes = pregeneratedKeyframes;\n        duration = t - sampleDelta;\n        ease = \"linear\";\n    }\n    const animation = (0,_index_mjs__WEBPACK_IMPORTED_MODULE_3__.animateStyle)(value.owner.current, valueName, keyframes, {\n        ...options,\n        duration,\n        /**\n         * This function is currently not called if ease is provided\n         * as a function so the cast is safe.\n         *\n         * However it would be possible for a future refinement to port\n         * in easing pregeneration from Motion One for browsers that\n         * support the upcoming `linear()` easing function.\n         */\n        ease: ease,\n        times,\n    });\n    const cancelAnimation = () => {\n        pendingCancel = false;\n        animation.cancel();\n    };\n    const safeCancel = () => {\n        pendingCancel = true;\n        _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_4__.frame.update(cancelAnimation);\n        resolveFinishedPromise();\n        updateFinishedPromise();\n    };\n    /**\n     * Prefer the `onfinish` prop as it's more widely supported than\n     * the `finished` promise.\n     *\n     * Here, we synchronously set the provided MotionValue to the end\n     * keyframe. If we didn't, when the WAAPI animation is finished it would\n     * be removed from the element which would then revert to its old styles.\n     */\n    animation.onfinish = () => {\n        if (pendingCancel)\n            return;\n        value.set((0,_utils_get_final_keyframe_mjs__WEBPACK_IMPORTED_MODULE_5__.getFinalKeyframe)(keyframes, options));\n        onComplete && onComplete();\n        safeCancel();\n    };\n    /**\n     * Animation interrupt callback.\n     */\n    const controls = {\n        then(resolve, reject) {\n            return currentFinishedPromise.then(resolve, reject);\n        },\n        attachTimeline(timeline) {\n            animation.timeline = timeline;\n            animation.onfinish = null;\n            return _utils_noop_mjs__WEBPACK_IMPORTED_MODULE_6__.noop;\n        },\n        get time() {\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__.millisecondsToSeconds)(animation.currentTime || 0);\n        },\n        set time(newTime) {\n            animation.currentTime = (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__.secondsToMilliseconds)(newTime);\n        },\n        get speed() {\n            return animation.playbackRate;\n        },\n        set speed(newSpeed) {\n            animation.playbackRate = newSpeed;\n        },\n        get duration() {\n            return (0,_utils_time_conversion_mjs__WEBPACK_IMPORTED_MODULE_7__.millisecondsToSeconds)(duration);\n        },\n        play: () => {\n            if (hasStopped)\n                return;\n            animation.play();\n            /**\n             * Cancel any pending cancel tasks\n             */\n            (0,_frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_4__.cancelFrame)(cancelAnimation);\n        },\n        pause: () => animation.pause(),\n        stop: () => {\n            hasStopped = true;\n            if (animation.playState === \"idle\")\n                return;\n            /**\n             * WAAPI doesn't natively have any interruption capabilities.\n             *\n             * Rather than read commited styles back out of the DOM, we can\n             * create a renderless JS animation and sample it twice to calculate\n             * its current value, \"previous\" value, and therefore allow\n             * Motion to calculate velocity for any subsequent animation.\n             */\n            const { currentTime } = animation;\n            if (currentTime) {\n                const sampleAnimation = (0,_js_index_mjs__WEBPACK_IMPORTED_MODULE_2__.animateValue)({\n                    ...options,\n                    autoplay: false,\n                });\n                value.setWithVelocity(sampleAnimation.sample(currentTime - sampleDelta).value, sampleAnimation.sample(currentTime).value, sampleDelta);\n            }\n            safeCancel();\n        },\n        complete: () => {\n            if (pendingCancel)\n                return;\n            animation.finish();\n        },\n        cancel: safeCancel,\n    };\n    return controls;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/create-accelerated-animation.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cubicBezierAsString: function() { return /* binding */ cubicBezierAsString; },\n/* harmony export */   isWaapiSupportedEasing: function() { return /* binding */ isWaapiSupportedEasing; },\n/* harmony export */   mapEasingToNativeEasing: function() { return /* binding */ mapEasingToNativeEasing; },\n/* harmony export */   supportedWaapiEasing: function() { return /* binding */ supportedWaapiEasing; }\n/* harmony export */ });\n/* harmony import */ var _easing_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../easing/utils/is-bezier-definition.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/easing/utils/is-bezier-definition.mjs\");\n\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean(!easing ||\n        (typeof easing === \"string\" && supportedWaapiEasing[easing]) ||\n        (0,_easing_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing) {\n    if (!easing)\n        return undefined;\n    return (0,_easing_utils_is_bezier_definition_mjs__WEBPACK_IMPORTED_MODULE_0__.isBezierDefinition)(easing)\n        ? cubicBezierAsString(easing)\n        : Array.isArray(easing)\n            ? easing.map(mapEasingToNativeEasing)\n            : supportedWaapiEasing[easing];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs ***!
  \********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   animateStyle: function() { return /* binding */ animateStyle; }\n/* harmony export */ });\n/* harmony import */ var _easing_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./easing.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs\");\n\n\nfunction animateStyle(element, valueName, keyframes, { delay = 0, duration, repeat = 0, repeatType = \"loop\", ease, times, } = {}) {\n    const keyframeOptions = { [valueName]: keyframes };\n    if (times)\n        keyframeOptions.offset = times;\n    const easing = (0,_easing_mjs__WEBPACK_IMPORTED_MODULE_0__.mapEasingToNativeEasing)(ease);\n    /**\n     * If this is an easing array, apply to keyframes, not animation as a whole\n     */\n    if (Array.isArray(easing))\n        keyframeOptions.easing = easing;\n    return element.animate(keyframeOptions, {\n        delay,\n        duration,\n        easing: !Array.isArray(easing) ? easing : \"linear\",\n        fill: \"both\",\n        iterations: repeat + 1,\n        direction: repeatType === \"reverse\" ? \"alternate\" : \"normal\",\n    });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy93YWFwaS9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBdUQ7O0FBRXZELHVEQUF1RCxxRUFBcUUsSUFBSTtBQUNoSSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBLG1CQUFtQixvRUFBdUI7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUV3QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2FuaW1hdGlvbi9hbmltYXRvcnMvd2FhcGkvaW5kZXgubWpzP2E0MTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbWFwRWFzaW5nVG9OYXRpdmVFYXNpbmcgfSBmcm9tICcuL2Vhc2luZy5tanMnO1xuXG5mdW5jdGlvbiBhbmltYXRlU3R5bGUoZWxlbWVudCwgdmFsdWVOYW1lLCBrZXlmcmFtZXMsIHsgZGVsYXkgPSAwLCBkdXJhdGlvbiwgcmVwZWF0ID0gMCwgcmVwZWF0VHlwZSA9IFwibG9vcFwiLCBlYXNlLCB0aW1lcywgfSA9IHt9KSB7XG4gICAgY29uc3Qga2V5ZnJhbWVPcHRpb25zID0geyBbdmFsdWVOYW1lXToga2V5ZnJhbWVzIH07XG4gICAgaWYgKHRpbWVzKVxuICAgICAgICBrZXlmcmFtZU9wdGlvbnMub2Zmc2V0ID0gdGltZXM7XG4gICAgY29uc3QgZWFzaW5nID0gbWFwRWFzaW5nVG9OYXRpdmVFYXNpbmcoZWFzZSk7XG4gICAgLyoqXG4gICAgICogSWYgdGhpcyBpcyBhbiBlYXNpbmcgYXJyYXksIGFwcGx5IHRvIGtleWZyYW1lcywgbm90IGFuaW1hdGlvbiBhcyBhIHdob2xlXG4gICAgICovXG4gICAgaWYgKEFycmF5LmlzQXJyYXkoZWFzaW5nKSlcbiAgICAgICAga2V5ZnJhbWVPcHRpb25zLmVhc2luZyA9IGVhc2luZztcbiAgICByZXR1cm4gZWxlbWVudC5hbmltYXRlKGtleWZyYW1lT3B0aW9ucywge1xuICAgICAgICBkZWxheSxcbiAgICAgICAgZHVyYXRpb24sXG4gICAgICAgIGVhc2luZzogIUFycmF5LmlzQXJyYXkoZWFzaW5nKSA/IGVhc2luZyA6IFwibGluZWFyXCIsXG4gICAgICAgIGZpbGw6IFwiYm90aFwiLFxuICAgICAgICBpdGVyYXRpb25zOiByZXBlYXQgKyAxLFxuICAgICAgICBkaXJlY3Rpb246IHJlcGVhdFR5cGUgPT09IFwicmV2ZXJzZVwiID8gXCJhbHRlcm5hdGVcIiA6IFwibm9ybWFsXCIsXG4gICAgfSk7XG59XG5cbmV4cG9ydCB7IGFuaW1hdGVTdHlsZSB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs":
/*!***************************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs ***!
  \***************************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFinalKeyframe: function() { return /* binding */ getFinalKeyframe; }\n/* harmony export */ });\nfunction getFinalKeyframe(keyframes, { repeat, repeatType = \"loop\" }) {\n    const index = repeat && repeatType !== \"loop\" && repeat % 2 === 1\n        ? 0\n        : keyframes.length - 1;\n    return keyframes[index];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy93YWFwaS91dGlscy9nZXQtZmluYWwta2V5ZnJhbWUubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSx1Q0FBdUMsNkJBQTZCO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTRCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvYW5pbWF0aW9uL2FuaW1hdG9ycy93YWFwaS91dGlscy9nZXQtZmluYWwta2V5ZnJhbWUubWpzPzA1ZTUiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZ2V0RmluYWxLZXlmcmFtZShrZXlmcmFtZXMsIHsgcmVwZWF0LCByZXBlYXRUeXBlID0gXCJsb29wXCIgfSkge1xuICAgIGNvbnN0IGluZGV4ID0gcmVwZWF0ICYmIHJlcGVhdFR5cGUgIT09IFwibG9vcFwiICYmIHJlcGVhdCAlIDIgPT09IDFcbiAgICAgICAgPyAwXG4gICAgICAgIDoga2V5ZnJhbWVzLmxlbmd0aCAtIDE7XG4gICAgcmV0dXJuIGtleWZyYW1lc1tpbmRleF07XG59XG5cbmV4cG9ydCB7IGdldEZpbmFsS2V5ZnJhbWUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs\n"));

/***/ })

}]);