'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { getProducts, getProduct, getCategories } from '@/lib/woocommerce';
import { useCartStore } from '@/lib/wooStore';
import { login, register, getCurrentUser } from '@/lib/wooAuth';
import { Loader2 } from 'lucide-react';

const TestSection = ({ title, children }: { title: string; children: React.ReactNode }) => (
  <div className="mb-8 border rounded-md p-4">
    <h2 className="text-lg font-medium mb-4">{title}</h2>
    {children}
  </div>
);

const WooCommerceTest = () => {
  const [products, setProducts] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [singleProduct, setSingleProduct] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [results, setResults] = useState<Record<string, string>>({});
  
  const cartStore = useCartStore();
  
  const setLoadingState = (key: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [key]: isLoading }));
  };
  
  const setResultState = (key: string, result: string) => {
    setResults(prev => ({ ...prev, [key]: result }));
  };
  
  // Test fetching products
  const testGetProducts = async () => {
    try {
      setLoadingState('products', true);
      const data = await getProducts();
      setProducts(data.nodes || []);
      setResultState('products', `Success! Fetched ${data.nodes?.length || 0} products`);
    } catch (error) {
      console.error('Error fetching products:', error);
      setResultState('products', `Error: ${(error as Error).message}`);
    } finally {
      setLoadingState('products', false);
    }
  };
  
  // Test fetching categories
  const testGetCategories = async () => {
    try {
      setLoadingState('categories', true);
      const data = await getCategories();
      setCategories(data.nodes || []);
      setResultState('categories', `Success! Fetched ${data.nodes?.length || 0} categories`);
    } catch (error) {
      console.error('Error fetching categories:', error);
      setResultState('categories', `Error: ${(error as Error).message}`);
    } finally {
      setLoadingState('categories', false);
    }
  };
  
  // Test fetching a single product
  const testGetProduct = async () => {
    if (!products.length) {
      setResultState('product', 'Error: No products available to test with');
      return;
    }
    
    try {
      setLoadingState('product', true);
      const productId = products[0].databaseId;
      const data = await getProduct(productId);
      setSingleProduct(data);
      setResultState('product', `Success! Fetched product: ${data.name}`);
    } catch (error) {
      console.error('Error fetching product:', error);
      setResultState('product', `Error: ${(error as Error).message}`);
    } finally {
      setLoadingState('product', false);
    }
  };
  
  // Test adding to cart
  const testAddToCart = async () => {
    if (!products.length) {
      setResultState('cart', 'Error: No products available to test with');
      return;
    }
    
    try {
      setLoadingState('cart', true);
      const product = products[0];
      
      await cartStore.addToCart({
        productId: product.databaseId.toString(),
        name: product.name,
        price: product.price,
        quantity: 1,
        image: {
          url: product.image?.sourceUrl || '',
          altText: product.image?.altText || product.name
        }
      });
      
      setResultState('cart', `Success! Added ${product.name} to cart`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      setResultState('cart', `Error: ${(error as Error).message}`);
    } finally {
      setLoadingState('cart', false);
    }
  };
  
  // Test user authentication
  const testLogin = async () => {
    try {
      setLoadingState('login', true);
      // Use test credentials - in a real app, these would be entered by the user
      const data = await login('<EMAIL>', 'password123');
      if (data) {
        setUser(data);
        setResultState('login', `Success! Logged in as ${data.email}`);
      }
    } catch (error) {
      console.error('Error logging in:', error);
      setResultState('login', `Error: ${(error as Error).message}`);
    } finally {
      setLoadingState('login', false);
    }
  };
  
  // Test user registration
  const testRegister = async () => {
    try {
      setLoadingState('register', true);
      // Generate a random email to avoid conflicts
      const randomEmail = `test${Math.floor(Math.random() * 10000)}@example.com`;
      const data = await register({
        email: randomEmail,
        firstName: 'Test',
        lastName: 'User',
        password: 'password123',
        username: `testuser${Math.floor(Math.random() * 10000)}`
      });
      setResultState('register', `Success! Registered user: ${randomEmail}`);
    } catch (error) {
      console.error('Error registering:', error);
      setResultState('register', `Error: ${(error as Error).message}`);
    } finally {
      setLoadingState('register', false);
    }
  };
  
  // Test getting current user
  const testGetCurrentUser = async () => {
    try {
      setLoadingState('currentUser', true);
      const data = await getCurrentUser();
      if (data) {
        setUser(data);
        setResultState('currentUser', `Success! Current user: ${data.email}`);
      } else {
        setResultState('currentUser', 'No user is currently logged in');
      }
    } catch (error) {
      console.error('Error getting current user:', error);
      setResultState('currentUser', `Error: ${(error as Error).message}`);
    } finally {
      setLoadingState('currentUser', false);
    }
  };
  
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">WooCommerce Integration Test</h1>
      
      <TestSection title="Products">
        <div className="space-y-4">
          <Button 
            onClick={testGetProducts}
            disabled={loading.products}
          >
            {loading.products && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Fetch Products
          </Button>
          
          {results.products && (
            <div className={`p-3 rounded-md ${results.products.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {results.products}
            </div>
          )}
          
          {products.length > 0 && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">First 5 Products:</h3>
              <ul className="list-disc pl-5 space-y-1">
                {products.slice(0, 5).map(product => (
                  <li key={product.id}>{product.name} - ${product.price}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </TestSection>
      
      <TestSection title="Categories">
        <div className="space-y-4">
          <Button 
            onClick={testGetCategories}
            disabled={loading.categories}
          >
            {loading.categories && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Fetch Categories
          </Button>
          
          {results.categories && (
            <div className={`p-3 rounded-md ${results.categories.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {results.categories}
            </div>
          )}
          
          {categories.length > 0 && (
            <div className="mt-4">
              <h3 className="font-medium mb-2">Categories:</h3>
              <ul className="list-disc pl-5 space-y-1">
                {categories.map(category => (
                  <li key={category.id}>{category.name}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </TestSection>
      
      <TestSection title="Single Product">
        <div className="space-y-4">
          <Button 
            onClick={testGetProduct}
            disabled={loading.product || !products.length}
          >
            {loading.product && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Fetch Single Product
          </Button>
          
          {results.product && (
            <div className={`p-3 rounded-md ${results.product.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {results.product}
            </div>
          )}
          
          {singleProduct && (
            <div className="mt-4 p-4 border rounded-md">
              <h3 className="font-medium text-lg">{singleProduct.name}</h3>
              <p className="text-gray-500 mt-1">${singleProduct.price}</p>
              {singleProduct.image && (
                <div className="mt-2 w-32 h-32 relative">
                  <img 
                    src={singleProduct.image.sourceUrl} 
                    alt={singleProduct.image.altText || singleProduct.name}
                    className="object-cover w-full h-full"
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </TestSection>
      
      <TestSection title="Cart">
        <div className="space-y-4">
          <Button 
            onClick={testAddToCart}
            disabled={loading.cart || !products.length}
          >
            {loading.cart && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Add to Cart
          </Button>
          
          {results.cart && (
            <div className={`p-3 rounded-md ${results.cart.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {results.cart}
            </div>
          )}
          
          <div className="mt-4">
            <h3 className="font-medium mb-2">Cart Items: {cartStore.items.length}</h3>
            {cartStore.items.length > 0 && (
              <ul className="list-disc pl-5 space-y-1">
                {cartStore.items.map(item => (
                  <li key={item.id}>{item.name} - Qty: {item.quantity}</li>
                ))}
              </ul>
            )}
          </div>
        </div>
      </TestSection>
      
      <TestSection title="Authentication">
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={testLogin}
              disabled={loading.login}
            >
              {loading.login && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Test Login
            </Button>
            
            <Button 
              onClick={testRegister}
              disabled={loading.register}
              variant="outline"
            >
              {loading.register && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Test Register
            </Button>
            
            <Button 
              onClick={testGetCurrentUser}
              disabled={loading.currentUser}
              variant="secondary"
            >
              {loading.currentUser && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Get Current User
            </Button>
          </div>
          
          {results.login && (
            <div className={`p-3 rounded-md ${results.login.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {results.login}
            </div>
          )}
          
          {results.register && (
            <div className={`p-3 rounded-md ${results.register.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {results.register}
            </div>
          )}
          
          {results.currentUser && (
            <div className={`p-3 rounded-md ${results.currentUser.includes('Error') ? 'bg-red-50 text-red-700' : 'bg-green-50 text-green-700'}`}>
              {results.currentUser}
            </div>
          )}
          
          {user && (
            <div className="mt-4 p-4 border rounded-md">
              <h3 className="font-medium">Current User:</h3>
              <p>Email: {user.email}</p>
              <p>Name: {user.firstName} {user.lastName}</p>
            </div>
          )}
        </div>
      </TestSection>
    </div>
  );
};

export default WooCommerceTest; 