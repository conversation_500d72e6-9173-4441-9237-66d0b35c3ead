import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { jwtDecode } from 'jwt-decode';
import { GraphQLClient, gql } from 'graphql-request';
import AccountDashboard from '@/components/account/AccountDashboard';

export const metadata: Metadata = {
  title: 'My Account | Ankkor',
  description: 'View your account details, order history, and manage your profile.',
};

// Define WooCommerce Customer interface for API responses
interface WooCustomer {
  id: string;
  databaseId: number;
  email: string;
  firstName: string;
  lastName: string;
  displayName?: string;
  username?: string;
  role?: string;
  date?: string;
  modified?: string;
  isPayingCustomer?: boolean;
  orderCount?: number;
  totalSpent?: number;
  billing?: {
    firstName: string;
    lastName: string;
    company: string;
    address1: string;
    address2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
    email: string;
    phone: string;
  };
  shipping?: {
    firstName: string;
    lastName: string;
    company: string;
    address1: string;
    address2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  orders?: {
    nodes: Array<{
      id: string;
      databaseId: number;
      date: string;
      status: string;
      total: string;
      subtotal?: string;
      totalTax?: string;
      shippingTotal?: string;
      discountTotal?: string;
      paymentMethodTitle?: string;
      customerNote?: string;
      billing?: {
        firstName: string;
        lastName: string;
        company: string;
        address1: string;
        address2: string;
        city: string;
        state: string;
        postcode: string;
        country: string;
        email: string;
        phone: string;
      };
      shipping?: {
        firstName: string;
        lastName: string;
        company: string;
        address1: string;
        address2: string;
        city: string;
        state: string;
        postcode: string;
        country: string;
      };
      lineItems: {
        nodes: Array<{
          product: {
            node: {
              id: string;
              name: string;
              slug?: string;
              image?: {
                sourceUrl: string;
                altText: string;
              };
            };
          };
          variation?: {
            node: {
              id: string;
              name: string;
              attributes?: {
                nodes: Array<{
                  name: string;
                  value: string;
                }>;
              };
            };
          };
          quantity: number;
          total: string;
          subtotal?: string;
          totalTax?: string;
        }>;
      };
      shippingLines?: {
        nodes: Array<{
          methodTitle: string;
          total: string;
        }>;
      };
      feeLines?: {
        nodes: Array<{
          name: string;
          total: string;
        }>;
      };
      couponLines?: {
        nodes: Array<{
          code: string;
          discount: string;
        }>;
      };
    }>;
  };
  downloadableItems?: {
    nodes: Array<{
      name: string;
      downloadId: string;
      downloadsRemaining?: number;
      accessExpires?: string;
      product: {
        node: {
          id: string;
          name: string;
        };
      };
    }>;
  };
  metaData?: Array<{
    key: string;
    value: string;
  }>;
}

// GraphQL endpoint
const endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || 'https://your-wordpress-site.com/graphql';

// Get customer query
const GET_CUSTOMER_QUERY = gql`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      username
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`;

async function getCustomerData(authToken: string) {
  // Create GraphQL client with auth token
  const graphQLClient = new GraphQLClient(endpoint, {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': `Bearer ${authToken}`
    },
  });

  try {
    const data = await graphQLClient.request<{
      customer: WooCustomer;
    }>(GET_CUSTOMER_QUERY);
    
    return {
      success: true,
      customer: data.customer
    };
  } catch (error) {
    console.error('Error fetching customer data:', error);
    return {
      success: false,
      error: 'Failed to fetch customer data'
    };
  }
}

export default async function AccountPage() {
  // Check if user is authenticated by verifying the auth cookie
  const cookieStore = cookies();
  const authCookie = cookieStore.get('woo_auth_token');
  
  if (!authCookie || !authCookie.value) {
    // Redirect to login page if not authenticated
    redirect('/sign-in?redirect=/account');
  }
  
  // Verify the token is valid and not expired
  try {
    const decodedToken = jwtDecode<{exp: number}>(authCookie.value);
    const currentTime = Math.floor(Date.now() / 1000);
    
    // If token is expired, redirect to login
    if (decodedToken.exp < currentTime) {
      redirect('/sign-in?redirect=/account&reason=expired');
    }
  } catch (error) {
    console.error('Invalid JWT token:', error);
    redirect('/sign-in?redirect=/account&reason=invalid');
  }
  
  // Get customer data using the auth token
  const customerResponse = await getCustomerData(authCookie.value);
  const customer = customerResponse.success ? customerResponse.customer : null;
  
  // If we couldn't get customer data despite having a valid token,
  // there might be an issue with the token or the WooCommerce API
  if (!customer) {
    // Option 1: Show error message
    return (
      <div className="container mx-auto py-12 px-4">
        <h1 className="text-3xl font-serif mb-8">My Account</h1>
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded">
          Unable to load account information. Please try <a href="/sign-in" className="underline">signing in again</a>.
        </div>
      </div>
    );
    
    // Option 2: Redirect to login (uncomment to use this approach instead)
    // redirect('/sign-in?redirect=/account&reason=fetch_error');
  }
  
  // Transform WooCustomer to the expected shape for the AccountDashboard component
  const transformedCustomer = {
    ...customer,
    displayName: customer.username || `${customer.firstName} ${customer.lastName}`.trim()
  };
  
  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-3xl font-serif mb-8">My Account</h1>
      
      {customer ? (
        <AccountDashboard customer={transformedCustomer} />
      ) : (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded">
          Unable to load account information. Please try again later.
        </div>
      )}
    </div>
  );
} 