Stack trace:
Frame         Function      Args
0007FFFF9B80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8A80) msys-2.0.dll+0x2118E
0007FFFF9B80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9E58) msys-2.0.dll+0x69BA
0007FFFF9B80  0002100469F2 (00021028DF99, 0007FFFF9A38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9B80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9B80  00021006A545 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9E60  00021006B9A5 (0007FFFF9B90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEC1EC0000 ntdll.dll
7FFEC0370000 KERNEL32.DLL
7FFEBF370000 KERNELBASE.dll
7FFEC0F00000 USER32.dll
7FFEBF160000 win32u.dll
7FFEBFD80000 GDI32.dll
7FFEBFB20000 gdi32full.dll
7FFEBF2C0000 msvcp_win.dll
7FFEBF010000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFEBFF20000 advapi32.dll
7FFEC0B00000 msvcrt.dll
7FFEC0E50000 sechost.dll
7FFEBFC60000 RPCRT4.dll
7FFEBE610000 CRYPTBASE.DLL
7FFEBF190000 bcryptPrimitives.dll
7FFEBFEA0000 IMM32.DLL
