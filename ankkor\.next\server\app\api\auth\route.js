"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/route";
exports.ids = ["app/api/auth/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "graphql-request":
/*!**********************************!*\
  !*** external "graphql-request" ***!
  \**********************************/
/***/ ((module) => {

module.exports = import("graphql-request");;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/route.ts */ \"(rsc)/./src/app/api/auth/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([E_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nE_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/route\",\n        pathname: \"/api/auth\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\auth\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_auth_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/auth/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/auth/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/auth/route.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! graphql-request */ \"graphql-request\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jwt-decode */ \"(rsc)/./node_modules/jwt-decode/build/esm/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([graphql_request__WEBPACK_IMPORTED_MODULE_2__]);\ngraphql_request__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Auth token cookie name\nconst AUTH_COOKIE_NAME = \"woo_auth_token\";\nconst REFRESH_COOKIE_NAME = \"woo_refresh_token\";\n// GraphQL endpoint\nconst endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\";\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_2__.GraphQLClient(endpoint);\n// Login mutation\nconst LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_2__.gql)`\n  mutation LoginUser($username: String!, $password: String!) {\n    login(input: {\n      clientMutationId: \"login\"\n      username: $username\n      password: $password\n    }) {\n      authToken\n      refreshToken\n      user {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n`;\n// Register mutation\nconst REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_2__.gql)`\n  mutation RegisterUser($input: RegisterCustomerInput!) {\n    registerCustomer(input: $input) {\n      clientMutationId\n      authToken\n      refreshToken\n      customer {\n        id\n        databaseId\n        email\n        firstName\n        lastName\n      }\n    }\n  }\n`;\n// Refresh token mutation\nconst REFRESH_TOKEN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_2__.gql)`\n  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {\n    refreshJwtAuthToken(input: $input) {\n      authToken\n    }\n  }\n`;\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, ...data } = body;\n        switch(action){\n            case \"login\":\n                return handleLogin(data);\n            case \"register\":\n                return handleRegister(data);\n            case \"logout\":\n                return handleLogout();\n            case \"refresh\":\n                return handleRefreshToken(data);\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                    success: false,\n                    message: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Auth API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function handleLogin({ username, password }) {\n    try {\n        // GraphQL request to login\n        const data = await graphQLClient.request(LOGIN_MUTATION, {\n            username,\n            password\n        });\n        if (!data.login || !data.login.authToken) {\n            console.error(\"Login failed: No auth token returned\");\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Invalid credentials\"\n            }, {\n                status: 401\n            });\n        }\n        // Set auth tokens as HTTP-only cookies\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        // Configure cookie options\n        const cookieOptions = {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"lax\",\n            path: \"/\",\n            maxAge: 60 * 60 * 24 * 7\n        };\n        // Set auth token cookie\n        cookieStore.set(AUTH_COOKIE_NAME, data.login.authToken, cookieOptions);\n        // Set refresh token cookie if available\n        if (data.login.refreshToken) {\n            cookieStore.set(REFRESH_COOKIE_NAME, data.login.refreshToken, {\n                ...cookieOptions,\n                maxAge: 60 * 60 * 24 * 30\n            });\n        }\n        console.log(\"Login successful, cookies set for user:\", data.login.user.email);\n        // Create response with user data but not the tokens (for security)\n        const response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            user: {\n                id: data.login.user.id,\n                email: data.login.user.email,\n                firstName: data.login.user.firstName,\n                lastName: data.login.user.lastName\n            }\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        // Check for specific GraphQL errors\n        if (error.response?.errors) {\n            const graphqlErrors = error.response.errors;\n            console.error(\"GraphQL errors:\", graphqlErrors);\n            // Return first error message\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: graphqlErrors[0]?.message || \"Login failed\"\n            }, {\n                status: 401\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Login failed\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function handleRegister(userData) {\n    try {\n        const { email, firstName, lastName, password } = userData;\n        // Prepare input data for registration\n        const input = {\n            clientMutationId: \"registerCustomer\",\n            email,\n            firstName,\n            lastName,\n            password,\n            username: email\n        };\n        const data = await graphQLClient.request(REGISTER_MUTATION, {\n            input\n        });\n        if (!data.registerCustomer || !data.registerCustomer.authToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Registration failed\"\n            }, {\n                status: 400\n            });\n        }\n        // Set auth tokens as HTTP-only cookies\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        // Configure cookie options\n        const cookieOptions = {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"lax\",\n            path: \"/\",\n            maxAge: 60 * 60 * 24 * 7\n        };\n        // Set auth token cookie\n        cookieStore.set(AUTH_COOKIE_NAME, data.registerCustomer.authToken, cookieOptions);\n        // Set refresh token cookie if available\n        if (data.registerCustomer.refreshToken) {\n            cookieStore.set(REFRESH_COOKIE_NAME, data.registerCustomer.refreshToken, {\n                ...cookieOptions,\n                maxAge: 60 * 60 * 24 * 30\n            });\n        }\n        console.log(\"Registration successful, cookies set for user:\", email);\n        // Don't send tokens in the response to prevent XSS\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true,\n            customer: data.registerCustomer.customer\n        });\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        // Check for specific GraphQL errors\n        if (error.response?.errors) {\n            const graphqlErrors = error.response.errors;\n            console.error(\"GraphQL errors:\", graphqlErrors);\n            // Return first error message\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: graphqlErrors[0]?.message || \"Registration failed\"\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Registration failed\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function handleLogout() {\n    // Clear auth cookies\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n    // Delete auth token cookie\n    cookieStore.set(AUTH_COOKIE_NAME, \"\", {\n        expires: new Date(0),\n        path: \"/\",\n        secure: \"development\" === \"production\",\n        httpOnly: true,\n        sameSite: \"lax\"\n    });\n    // Delete refresh token cookie\n    cookieStore.set(REFRESH_COOKIE_NAME, \"\", {\n        expires: new Date(0),\n        path: \"/\",\n        secure: \"development\" === \"production\",\n        httpOnly: true,\n        sameSite: \"lax\"\n    });\n    console.log(\"Logout: Auth cookies cleared\");\n    return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n        success: true,\n        message: \"Logged out successfully\"\n    });\n}\nasync function handleRefreshToken({ refreshToken }) {\n    try {\n        // If no token provided, try to get from cookies\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n        const tokenFromCookie = cookieStore.get(REFRESH_COOKIE_NAME)?.value;\n        const token = refreshToken || tokenFromCookie;\n        if (!token) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"No refresh token provided\"\n            }, {\n                status: 400\n            });\n        }\n        // Request a new auth token using the refresh token\n        const data = await graphQLClient.request(REFRESH_TOKEN_MUTATION, {\n            input: {\n                clientMutationId: \"refreshToken\",\n                jwtRefreshToken: token\n            }\n        });\n        if (!data.refreshJwtAuthToken || !data.refreshJwtAuthToken.authToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: \"Failed to refresh token\"\n            }, {\n                status: 400\n            });\n        }\n        const newAuthToken = data.refreshJwtAuthToken.authToken;\n        // Set the new auth token as an HTTP-only cookie\n        cookieStore.set(AUTH_COOKIE_NAME, newAuthToken, {\n            httpOnly: true,\n            secure: \"development\" === \"production\",\n            sameSite: \"lax\",\n            path: \"/\",\n            maxAge: 60 * 60 * 24 * 7\n        });\n        // Try to extract user information from token\n        let userId = \"unknown\";\n        try {\n            const decodedToken = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_3__.jwtDecode)(newAuthToken);\n            userId = decodedToken.data?.user?.id || \"unknown\";\n        } catch (decodeError) {\n            console.error(\"Error decoding JWT token:\", decodeError);\n        }\n        console.log(\"Token refreshed successfully for user ID:\", userId);\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        // Check for specific GraphQL errors\n        if (error.response?.errors) {\n            const graphqlErrors = error.response.errors;\n            console.error(\"GraphQL errors:\", graphqlErrors);\n            // If the refresh token is invalid, clear the cookies\n            const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_0__.cookies)();\n            cookieStore.delete(AUTH_COOKIE_NAME);\n            cookieStore.delete(REFRESH_COOKIE_NAME);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n                success: false,\n                message: graphqlErrors[0]?.message || \"Token refresh failed\"\n            }, {\n                status: 401\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.json({\n            success: false,\n            message: error.message || \"Token refresh failed\"\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/jwt-decode"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Froute&page=%2Fapi%2Fauth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();