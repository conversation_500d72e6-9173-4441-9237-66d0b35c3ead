import { NextRequest, NextResponse } from 'next/server';

/**
 * GraphQL Proxy to handle CORS issues when connecting to WooCommerce
 * This endpoint forwards GraphQL requests to the WordPress site and handles CORS
 */
export async function POST(request: NextRequest) {
  try {
    // Get the GraphQL query from the request body
    const body = await request.json();

    // WordPress/WooCommerce GraphQL endpoint from env variables
    const graphqlEndpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || 'https://lightpink-eagle-376738.hostingersite.com/graphql';

    // Prepare headers for the request to WooCommerce
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Forward session token if present in the request
    const sessionHeader = request.headers.get('woocommerce-session');
    if (sessionHeader) {
      headers['woocommerce-session'] = sessionHeader;
    }

    // Forward the request to WordPress GraphQL
    const response = await fetch(graphqlEndpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(body),
    });

    // Get the response data
    const data = await response.json();

    // Prepare response headers
    const responseHeaders: Record<string, string> = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, woocommerce-session',
    };

    // Forward session token from WooCommerce response if present
    const responseSessionHeader = response.headers.get('woocommerce-session');
    if (responseSessionHeader) {
      responseHeaders['woocommerce-session'] = responseSessionHeader;
    }

    // Return the response with CORS headers
    return NextResponse.json(data, {
      status: response.status,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error('GraphQL proxy error:', error);
    return NextResponse.json(
      { 
        errors: [{ message: error instanceof Error ? error.message : 'Unknown error occurred' }] 
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, woocommerce-session',
        },
      }
    );
  }
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
} 