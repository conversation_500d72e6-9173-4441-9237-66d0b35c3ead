import React from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getProducts, getCategories } from '@/lib/woocommerce';
import ProductGrid from '@/components/product/ProductGrid';
import ProductCard from '@/components/product/ProductCard';

interface CategoryPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = params;
  
  // Fetch categories to find the current one
  const categoriesData = await getCategories();
  const category = categoriesData.nodes?.find((cat) => cat.slug === slug);
  
  if (!category) {
    return {
      title: 'Category Not Found | Ankkor',
      description: 'The requested category could not be found.'
    };
  }
  
  return {
    title: `${category.name} | Ankkor`,
    description: category.description || `Browse our collection of ${category.name.toLowerCase()} products.`
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = params;
  
  // Fetch categories to find the current one
  const categoriesData = await getCategories();
  const category = categoriesData.nodes?.find((cat) => cat.slug === slug);
  
  if (!category) {
    notFound();
  }
  
  // Fetch products for this category
  const productsData = await getProducts({
    first: 12,
    where: {
      categoryIn: [category.slug]
    }
  });
  
  const products = productsData.nodes || [];
  
  return (
    <div className="container mx-auto px-4 py-12">
      <header className="mb-8 text-center">
        <h1 className="text-3xl font-serif text-[#2c2c27] mb-2">{category.name}</h1>
        {category.description && (
          <p className="text-[#8a8778] max-w-2xl mx-auto">{category.description}</p>
        )}
        <p className="text-[#5c5c52] mt-2">{category.count} products</p>
      </header>
      
      {products.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500">No products found in this category.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {products.map((product) => (
            <ProductCard
              key={product.databaseId || product.id}
              id={product.databaseId?.toString() || product.id}
              name={product.name}
              price={product.salePrice || product.price || '0.00'}
              image={product.image?.sourceUrl || '/placeholder-product.jpg'}
              slug={product.slug}
              material={product.attributes?.nodes?.find((attr: any) => attr.name === 'Material')?.options?.[0] || 'Premium Fabric'}
              isNew={false}
              stockStatus={product.stockStatus || 'IN_STOCK'}
              compareAtPrice={product.regularPrice !== product.price ? product.regularPrice : null}
              regularPrice={product.regularPrice}
              salePrice={product.salePrice}
              onSale={product.onSale || false}
              shortDescription={product.shortDescription}
              type={product.type}
            />
          ))}
        </div>
      )}
    </div>
  );
} 