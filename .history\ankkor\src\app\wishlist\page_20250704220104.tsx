'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Heart, X, ShoppingBag, Check, ArrowRight, Trash2 } from 'lucide-react';
import { useCartStore, useWishlistStore } from '@/lib/store';
import { useCustomer } from '@/components/providers/CustomerProvider';
import Loader from '@/components/ui/loader';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { Toaster } from 'react-hot-toast';
import { Button } from '@/components/ui/button';

// Sample wishlist data (would normally be stored in a database or localStorage)
const sampleWishlistItems = [
  {
    id: 'prod_1',
    name: 'Oxford Dress Shirt',
    price: '4999.00',
    image: 'https://images.unsplash.com/photo-1598033129183-c4f50c736f10?q=80',
    handle: 'oxford-dress-shirt',
    material: 'Egyptian Cotton',
    variantId: 'gid://shopify/ProductVariant/1',
  },
  {
    id: 'prod_7',
    name: 'Wool Dress Pan<PERSON>',
    price: '5999.00',
    image: 'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?q=80',
    handle: 'wool-dress-pants',
    material: 'Italian Wool',
    variantId: 'gid://shopify/ProductVariant/7',
  },
  {
    id: 'prod_13',
    name: 'Pima Cotton Polo',
    price: '3499.00',
    image: 'https://images.unsplash.com/photo-1591047139829-d91aecb6caea?q=80',
    handle: 'pima-cotton-polo',
    material: 'Pima Cotton',
    variantId: 'gid://shopify/ProductVariant/13',
  },
];

export default function WishlistPage() {
  const cart = useCartStore();
  const { items: wishlistItems, removeFromWishlist, clearWishlist } = useWishlistStore();
  const { isAuthenticated, isLoading: customerLoading } = useCustomer();
  const [isLoading, setIsLoading] = useState(true);
  const [addedItems, setAddedItems] = useState<Record<string, boolean>>({});
  const [showSignupPrompt, setShowSignupPrompt] = useState(false);
  
  // Show signup prompt for guest users with items
  useEffect(() => {
    if (!isAuthenticated && wishlistItems.length > 0 && !customerLoading) {
      // Only show the prompt after a delay and if user has at least one item
      const timer = setTimeout(() => {
        setShowSignupPrompt(true);
      }, 3000); // Show after 3 seconds
      
      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, wishlistItems.length, customerLoading]);
  
  // Simulate loading delay
  useEffect(() => {
    if (!customerLoading) {
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [customerLoading]);
  
  // Add item to cart and optionally remove from wishlist
  const handleAddToCart = (item: typeof wishlistItems[0], removeAfterAdd: boolean = false) => {
    try {
      // Check if variantId exists and is a non-empty string
      if (!item.variantId || typeof item.variantId !== 'string' || item.variantId.trim() === '') {
        console.error('Invalid variant ID:', item.variantId);
        toast.error('Unable to add this item to your cart. Invalid product variant.');
        return;
      }

      // Ensure variantId is properly formatted as a Shopify Global ID if it's not already
      let shopifyVariantId = item.variantId;
      
      // Clean and format the variant ID
      if (!shopifyVariantId.startsWith('gid://')) {
        try {
          // Extract numeric ID if possible
          const numericId = shopifyVariantId.replace(/\D/g, '');
          if (!numericId) {
            throw new Error(`Could not extract a valid numeric ID from "${shopifyVariantId}"`);
          }
          // Format as a Shopify Global ID
          shopifyVariantId = `gid://shopify/ProductVariant/${numericId}`;
        } catch (error) {
          console.error('Failed to format variant ID:', error);
          toast.error('This product has an invalid variant ID format.');
          return;
        }
      }
      
      console.log(`Adding item to cart: ${item.name} with variant ID: ${shopifyVariantId}`);
      
      cart.addItem({
        productId: item.id,
        variantId: shopifyVariantId, // Use the properly formatted Shopify variant ID
        title: item.name,
        handle: item.handle,
        image: item.image,
        price: item.price,
        quantity: 1,
        currencyCode: 'INR'
      })
      .then(() => {
        // On success
        toast.success(`${item.name} added to your cart!`);
        
        // Show visual feedback
        setAddedItems(prev => ({ ...prev, [item.id]: true }));
        
        // Reset visual feedback after 2 seconds
        setTimeout(() => {
          setAddedItems(prev => ({ ...prev, [item.id]: false }));
        }, 2000);
        
        if (removeAfterAdd) {
          removeFromWishlist(item.id);
        }
      })
      .catch((error) => {
        console.error('Error from cart.addItem:', error);
        
        // Provide specific error message based on the error
        if (error.message?.includes('variant is no longer available')) {
          toast.error('This product is no longer available in the store.');
        } else if (error.message?.includes('Invalid variant ID')) {
          toast.error('This product has an invalid variant format. Please try another item.');
        } else {
          toast.error('Unable to add this item to your cart. Please try again later.');
        }
      });
    } catch (error) {
      console.error('Error in handleAddToCart:', error);
      toast.error('An unexpected error occurred. Please try again later.');
    }
  };
  
  // Add all items to cart
  const addAllToCart = async () => {
    try {
      if (wishlistItems.length === 0) {
        toast.error('Your wishlist is empty');
        return;
      }

      setIsLoading(true);
      
      // Create a loading toast that we'll update with progress
      const loadingToastId = toast.loading('Adding items to your cart...');
      
      let successCount = 0;
      let errorCount = 0;
      
      // Process items sequentially to avoid race conditions
      for (const item of wishlistItems) {
        try {
          // Check if variantId exists and is valid
          if (!item.variantId || typeof item.variantId !== 'string' || item.variantId.trim() === '') {
            console.error('Invalid variant ID for item:', item.name, item.variantId);
            errorCount++;
            continue; // Skip this item
          }
          
          // Format the variant ID
          let variantId = item.variantId;
          if (!variantId.startsWith('gid://')) {
            try {
              // Extract numeric ID if possible
              const numericId = variantId.replace(/\D/g, '');
              if (!numericId) {
                throw new Error(`Could not extract a valid numeric ID from "${variantId}"`);
              }
              variantId = `gid://shopify/ProductVariant/${numericId}`;
            } catch (error) {
              console.error('Failed to format variant ID:', error);
              errorCount++;
              continue; // Skip this item
            }
          }
          
          // Add item to cart one at a time
          await cart.addItem({
            productId: item.id,
            variantId: variantId,
            title: item.name,
            handle: item.handle,
            image: item.image,
            price: item.price,
            quantity: 1,
            currencyCode: 'INR'
          });
          
          // Update success count and the loading toast with progress
          successCount++;
          toast.loading(`Adding items to cart: ${successCount}/${wishlistItems.length}`, { id: loadingToastId });
          
          // Small delay between requests to prevent rate limiting
          await new Promise(resolve => setTimeout(resolve, 300));
          
        } catch (error) {
          console.error(`Error adding item ${item.name} to cart:`, error);
          errorCount++;
        }
      }
      
      // Dismiss the loading toast
      toast.dismiss(loadingToastId);
      setIsLoading(false);
      
      // Show appropriate success/error messages
      if (successCount > 0 && errorCount > 0) {
        toast.success(`Added ${successCount} items to your cart`);
        toast.error(`${errorCount} items could not be added`);
      } else if (successCount > 0) {
        toast.success(`All ${successCount} items added to your cart`);
        // Open cart after adding all items
        setTimeout(() => cart.toggleCart(), 500);
      } else {
        toast.error('Unable to add items to your cart. Please try again later.');
      }
    } catch (error) {
      console.error('Error in addAllToCart:', error);
      setIsLoading(false);
      toast.dismiss();
      toast.error('An error occurred while adding items to your cart');
    }
  };
  
  const dismissSignupPrompt = () => {
    setShowSignupPrompt(false);
    // Remember this decision in session storage
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('wishlist_prompt_dismissed', 'true');
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-serif">My Wishlist</h1>
        
        {wishlistItems.length > 0 && (
          <Button
            variant="outline"
            onClick={clearWishlist}
            className="text-sm"
          >
            Clear All
          </Button>
        )}
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center py-24">
          <Loader size="lg" color="#8a8778" />
        </div>
      ) : (
        <>
          {/* Sign up prompt for guest users with items */}
          {!isAuthenticated && showSignupPrompt && wishlistItems.length > 0 && (
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8 p-4 border border-[#e5e2d9] bg-[#f8f8f5] rounded-md"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start">
                  <Heart className="h-5 w-5 text-[#8a8778] mt-1 mr-3" />
                  <div>
                    <h3 className="font-serif font-medium text-[#2c2c27]">Create an account to save your wishlist</h3>
                    <p className="text-sm text-[#5c5c52] mt-1">Your wishlist is currently stored on this device only. Create an account to access it from any device.</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Link href="/sign-up" className="text-sm text-[#2c2c27] font-medium hover:text-[#8a8778] transition-colors">
                    Sign Up
                  </Link>
                  <button 
                    onClick={dismissSignupPrompt}
                    className="text-[#8a8778] hover:text-[#2c2c27] transition-colors"
                    aria-label="Dismiss"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        
          {wishlistItems.length === 0 ? (
            <div className="text-center py-16">
              <div className="inline-flex justify-center items-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                <Heart className="h-8 w-8 text-gray-400" />
              </div>
              <h2 className="text-xl font-medium mb-2">Your wishlist is empty</h2>
              <p className="text-gray-500 mb-6">
                Add items you love to your wishlist. Review them anytime and easily move them to the cart.
              </p>
              <Link href="/categories">
                <Button>
                  Continue Shopping
                </Button>
              </Link>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {wishlistItems.map((item) => (
                  <div key={item.id} className="border rounded-md overflow-hidden">
                    <div className="relative">
                      <Link href={`/product/${item.handle}`}>
                        <div className="aspect-square relative bg-gray-100">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover"
                          />
                        </div>
                      </Link>
                      <button
                        onClick={() => removeFromWishlist(item.id)}
                        className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100"
                        aria-label="Remove from wishlist"
                      >
                        <Trash2 className="h-4 w-4 text-gray-600" />
                      </button>
                    </div>
                    
                    <div className="p-4">
                      <Link href={`/product/${item.handle}`}>
                        <h2 className="font-medium text-lg hover:underline">{item.name}</h2>
                      </Link>
                      <p className="text-gray-700 my-2">₹{parseFloat(item.price).toFixed(2)}</p>
                      
                      <Button
                        onClick={() => handleAddToCart(item)}
                        className="w-full mt-2 flex items-center justify-center gap-2"
                      >
                        <ShoppingBag className="h-4 w-4" />
                        Add to Cart
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-12 text-center">
                <Link href="/categories">
                  <Button variant="outline">Continue Shopping</Button>
                </Link>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead className="border-b border-[#e5e2d9]">
                    <tr>
                      <th className="py-4 text-left font-serif text-[#2c2c27]">Product</th>
                      <th className="py-4 text-left font-serif text-[#2c2c27]">Price</th>
                      <th className="py-4 text-center font-serif text-[#2c2c27]">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-[#e5e2d9]">
                    {wishlistItems.map((item) => (
                      <tr key={item.id} className="group">
                        <td className="py-6">
                          <div className="flex items-center">
                            <div className="relative mr-4 h-24 w-20 overflow-hidden bg-[#f4f3f0]">
                              <Link href={`/product/${item.handle}`}>
                                <Image
                                  src={item.image}
                                  alt={item.name}
                                  fill
                                  sizes="(max-width: 768px) 80px, 120px"
                                  className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
                                />
                              </Link>
                            </div>
                            <div>
                              <Link 
                                href={`/product/${item.handle}`}
                                className="font-serif text-lg text-[#2c2c27] hover:text-[#8a8778] transition-colors"
                              >
                                {item.name}
                              </Link>
                              <p className="text-sm text-[#8a8778]">{item.material}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-6 font-medium text-[#2c2c27]">
                          {item.price.toString().includes('₹') ? item.price : `₹${item.price}`}
                        </td>
                        <td className="py-6">
                          <div className="flex items-center justify-center space-x-4">
                            <motion.button
                              onClick={() => handleAddToCart(item)}
                              className={`${addedItems[item.id] ? 'bg-[#2c2c27] text-[#f4f3f0]' : 'text-[#2c2c27]'} p-2 rounded-full transition-colors hover:text-[#8a8778]`}
                              aria-label="Add to cart"
                              whileTap={{ scale: 0.95 }}
                            >
                              {addedItems[item.id] ? (
                                <Check className="h-5 w-5" />
                              ) : (
                                <ShoppingBag className="h-5 w-5" />
                              )}
                            </motion.button>
                            <motion.button
                              onClick={() => removeFromWishlist(item.id)}
                              className="text-[#2c2c27] p-2 rounded-full hover:text-[#8a8778] transition-colors"
                              aria-label="Remove from wishlist"
                              whileTap={{ scale: 0.95 }}
                            >
                              <X className="h-5 w-5" />
                            </motion.button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </>
      )}
      
      <Toaster 
        position="top-center" 
        toastOptions={{
          duration: 3000,
          style: {
            background: '#F8F8F5',
            color: '#2C2C27',
            border: '1px solid #E5E2D9',
          },
          success: {
            iconTheme: {
              primary: '#2C2C27',
              secondary: '#F8F8F5',
            },
          },
          error: {
            iconTheme: {
              primary: '#2C2C27',
              secondary: '#F8F8F5',
            },
          }
        }}
      />
    </div>
  );
} 