import React from 'react';
import { ShoppingBag } from 'lucide-react';

interface AddToCartButtonProps {
  onClick: (e: React.MouseEvent) => void;
  disabled?: boolean;
  isLoading?: boolean;
  className?: string;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({ 
  onClick, 
  disabled = false, 
  isLoading = false,
  className = ""
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`
        cart-btn
        w-full h-10 
        rounded-none
        border-none
        bg-[#2c2c27]
        flex items-center justify-center
        cursor-pointer
        transition-all duration-500
        overflow-hidden
        shadow-sm hover:shadow-md
        relative
        disabled:bg-gray-400 disabled:cursor-not-allowed
        ${className}
      `}
    >
      <div className="
        icon-container
        absolute -left-12
        w-8 h-8
        bg-transparent
        rounded-full
        flex items-center justify-center
        overflow-hidden
        z-10
        transition-all duration-500
        cart-btn:hover:translate-x-14 cart-btn:hover:rounded-lg
      ">
        <ShoppingBag 
          className="h-4 w-4 text-[#f4f3f0]" 
          fill="currentColor"
        />
      </div>
      
      <span className="
        cart-text
        h-full w-fit
        flex items-center justify-center
        text-[#f4f3f0]
        z-0
        transition-all duration-500
        text-sm font-medium tracking-wider uppercase
        cart-btn:hover:translate-x-2
      ">
        {disabled ? 'Out of Stock' : isLoading ? 'Adding...' : 'Add to Cart'}
      </span>
      
      <style jsx>{`
        .cart-btn:hover .icon-container {
          transform: translateX(56px);
          border-radius: 8px;
        }
        
        .cart-btn:hover .cart-text {
          transform: translateX(8px);
        }
        
        .cart-btn:active {
          transform: scale(0.95);
        }
        
        .cart-btn:disabled:hover .icon-container {
          transform: translateX(0);
        }
        
        .cart-btn:disabled:hover .cart-text {
          transform: translateX(0);
        }
      `}</style>
    </button>
  );
};

export default AddToCartButton;
