"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["commons-src_lib_a"],{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addCustomerAddress: function() { return /* binding */ addCustomerAddress; },\n/* harmony export */   deleteCustomerAddress: function() { return /* binding */ deleteCustomerAddress; },\n/* harmony export */   getCurrentCustomer: function() { return /* binding */ getCurrentCustomer; },\n/* harmony export */   getCustomerSession: function() { return /* binding */ getCustomerSession; },\n/* harmony export */   isCustomerLoggedIn: function() { return /* binding */ isCustomerLoggedIn; },\n/* harmony export */   loginCustomer: function() { return /* binding */ loginCustomer; },\n/* harmony export */   logoutCustomer: function() { return /* binding */ logoutCustomer; },\n/* harmony export */   registerCustomer: function() { return /* binding */ registerCustomer; },\n/* harmony export */   setCustomerDefaultAddress: function() { return /* binding */ setCustomerDefaultAddress; },\n/* harmony export */   updateCustomerAddress: function() { return /* binding */ updateCustomerAddress; },\n/* harmony export */   updateCustomerProfile: function() { return /* binding */ updateCustomerProfile; }\n/* harmony export */ });\n/* harmony import */ var _woocommerce__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n/**\n * Authentication module that works with the WooCommerce authentication API\n */ \n// Local storage keys\nconst AUTH_TOKEN_KEY = \"woo_auth_token\";\nconst REFRESH_TOKEN_KEY = \"woo_refresh_token\";\n/**\n * Register a new customer with WooCommerce\n * @param registration Customer registration data\n * @returns Customer data and access token if successful\n */ async function registerCustomer(registration) {\n    try {\n        const { email, password, firstName, lastName, phone, acceptsMarketing = false, address } = registration;\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createCustomer)({\n            email,\n            password,\n            firstName,\n            lastName,\n            phone,\n            acceptsMarketing\n        });\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        if (!result.authToken) {\n            throw new Error(\"Registration failed: No authentication token returned\");\n        }\n        // Store the token in localStorage\n        saveCustomerToken({\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        });\n        // Save refresh token if available\n        if (result.refreshToken) {\n            localStorage.setItem(REFRESH_TOKEN_KEY, result.refreshToken);\n        }\n        // Get customer data using the new token\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(result.authToken);\n        return {\n            customer,\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        };\n    } catch (error) {\n        console.error(\"Error registering customer:\", error);\n        throw error;\n    }\n}\n/**\n * Log in a customer with WooCommerce\n * @param credentials Customer credentials\n * @returns Customer data and access token if successful\n */ async function loginCustomer(credentials) {\n    try {\n        const { email, password } = credentials;\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.customerLogin)(email, password);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        if (!result.authToken) {\n            throw new Error(\"Login failed: No authentication token returned\");\n        }\n        // Store the token in localStorage\n        saveCustomerToken({\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        });\n        // Save refresh token if available\n        if (result.refreshToken) {\n            localStorage.setItem(REFRESH_TOKEN_KEY, result.refreshToken);\n        }\n        // Get customer data using the new token\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(result.authToken);\n        return {\n            customer,\n            accessToken: result.authToken,\n            expiresAt: calculateTokenExpiry(result.authToken)\n        };\n    } catch (error) {\n        console.error(\"Error logging in customer:\", error);\n        throw error;\n    }\n}\n/**\n * Calculate token expiry from JWT token\n * @param token JWT token\n * @returns ISO date string of expiry\n */ function calculateTokenExpiry(token) {\n    try {\n        // Basic JWT parsing - tokens have 3 parts separated by dots\n        const parts = token.split(\".\");\n        if (parts.length !== 3) {\n            // If token isn't valid JWT format, set default expiry to 1 day\n            const date = new Date();\n            date.setDate(date.getDate() + 1);\n            return date.toISOString();\n        }\n        // Decode the payload (middle part)\n        const payload = JSON.parse(atob(parts[1]));\n        // JWT uses 'exp' field for expiry timestamp (in seconds)\n        if (payload.exp) {\n            return new Date(payload.exp * 1000).toISOString();\n        } else {\n            // Fallback: set expiry to 1 day if no exp field\n            const date = new Date();\n            date.setDate(date.getDate() + 1);\n            return date.toISOString();\n        }\n    } catch (error) {\n        console.error(\"Error calculating token expiry:\", error);\n        // Fallback: set expiry to 1 day\n        const date = new Date();\n        date.setDate(date.getDate() + 1);\n        return date.toISOString();\n    }\n}\n/**\n * Update a customer's profile\n * @param customerData Customer data to update\n * @returns Updated customer data\n */ async function updateCustomerProfile(customerData) {\n    try {\n        // Use the API endpoint to update the profile since it has access to HTTP-only cookies\n        const response = await fetch(\"/api/auth/update-profile\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\",\n            body: JSON.stringify(customerData)\n        });\n        const result = await response.json();\n        if (!response.ok || !result.success) {\n            throw new Error(result.message || \"Profile update failed\");\n        }\n        return {\n            customer: result.customer,\n            accessToken: \"token_managed_by_server\",\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()\n        };\n    } catch (error) {\n        console.error(\"Error updating customer profile:\", error);\n        throw error;\n    }\n}\n/**\n * Add a new address for the customer\n * @param address Address data\n * @returns Updated customer data\n */ async function addCustomerAddress(address) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.createAddress)(session.accessToken, address);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer,\n            address: result.customerAddress\n        };\n    } catch (error) {\n        console.error(\"Error adding customer address:\", error);\n        throw error;\n    }\n}\n/**\n * Update an existing customer address\n * @param id Address ID\n * @param address Address data\n * @returns Updated customer data\n */ async function updateCustomerAddress(id, address) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.updateAddress)(session.accessToken, id, address);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer,\n            address: result.customerAddress\n        };\n    } catch (error) {\n        console.error(\"Error updating customer address:\", error);\n        throw error;\n    }\n}\n/**\n * Delete a customer address\n * @param id Address ID\n * @returns Updated customer data\n */ async function deleteCustomerAddress(id) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.deleteAddress)(session.accessToken, id);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer,\n            deletedAddressId: result.deletedCustomerAddressId\n        };\n    } catch (error) {\n        console.error(\"Error deleting customer address:\", error);\n        throw error;\n    }\n}\n/**\n * Set a default address for the customer\n * @param addressId Address ID\n * @returns Updated customer data\n */ async function setCustomerDefaultAddress(addressId) {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            throw new Error(\"No active customer session found\");\n        }\n        const result = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.setDefaultAddress)(session.accessToken, addressId);\n        if (result.customerUserErrors && result.customerUserErrors.length > 0) {\n            throw new Error(result.customerUserErrors[0].message);\n        }\n        // Get updated customer data\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return {\n            customer\n        };\n    } catch (error) {\n        console.error(\"Error setting default address:\", error);\n        throw error;\n    }\n}\n/**\n * Logout the current customer\n */ function logoutCustomer() {\n    if (typeof localStorage !== \"undefined\") {\n        localStorage.removeItem(AUTH_TOKEN_KEY);\n        localStorage.removeItem(REFRESH_TOKEN_KEY);\n    }\n    // Also clear cookies\n    deleteCookie(AUTH_TOKEN_KEY);\n    deleteCookie(REFRESH_TOKEN_KEY);\n}\n/**\n * Delete cookie by name\n */ function deleteCookie(name) {\n    if (typeof document !== \"undefined\") {\n        document.cookie = \"\".concat(name, \"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;\");\n    }\n}\n/**\n * Get the current customer session from cookies\n * @returns Customer session if active, null otherwise\n */ function getCustomerSession() {\n    try {\n        if (typeof document === \"undefined\") {\n            return null;\n        }\n        const token = getCookie(AUTH_TOKEN_KEY);\n        if (!token) {\n            return null;\n        }\n        // For now, we'll create a basic session object with the token\n        // In a real implementation, you might want to decode the JWT to get expiration\n        return {\n            accessToken: token,\n            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now\n        };\n    } catch (error) {\n        console.error(\"Error getting customer session:\", error);\n        return null;\n    }\n}\n/**\n * Get cookie value by name\n */ function getCookie(name) {\n    if (typeof document === \"undefined\") return null;\n    const nameEQ = \"\".concat(name, \"=\");\n    const ca = document.cookie.split(\";\");\n    for(let i = 0; i < ca.length; i++){\n        let c = ca[i];\n        while(c.charAt(0) === \" \")c = c.substring(1, c.length);\n        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n}\n/**\n * Save customer token to localStorage and cookies\n * @param session Customer session to save\n */ function saveCustomerToken(session) {\n    if (typeof localStorage !== \"undefined\") {\n        localStorage.setItem(AUTH_TOKEN_KEY, JSON.stringify(session));\n    }\n    // Also save to cookies for consistency with clientAuth\n    setCookie(AUTH_TOKEN_KEY, session.accessToken);\n}\n/**\n * Set cookie with name and value\n */ function setCookie(name, value) {\n    let days = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30;\n    if (typeof document !== \"undefined\") {\n        const secure =  false ? 0 : \"\";\n        const date = new Date();\n        date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);\n        const expires = \"; expires=\" + date.toUTCString();\n        document.cookie = \"\".concat(name, \"=\").concat(value).concat(expires, \"; path=/; SameSite=Lax\").concat(secure);\n    }\n}\n/**\n * Check if customer is logged in\n * @returns true if customer is logged in, false otherwise\n */ function isCustomerLoggedIn() {\n    return getCustomerSession() !== null;\n}\n/**\n * Get the current customer data\n * @returns Customer data if logged in, null otherwise\n */ async function getCurrentCustomer() {\n    try {\n        const session = getCustomerSession();\n        if (!session) {\n            return null;\n        }\n        const customer = await (0,_woocommerce__WEBPACK_IMPORTED_MODULE_0__.getCustomer)(session.accessToken);\n        return customer;\n    } catch (error) {\n        console.error(\"Error getting current customer:\", error);\n        // If there's an error, the token might be invalid\n        logoutCustomer();\n        return null;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/clientAuth.ts":
/*!*******************************!*\
  !*** ./src/lib/clientAuth.ts ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAuth: function() { return /* binding */ checkAuth; },\n/* harmony export */   getAuthToken: function() { return /* binding */ getAuthToken; },\n/* harmony export */   getCurrentCustomer: function() { return /* binding */ getCurrentCustomer; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   getRefreshToken: function() { return /* binding */ getRefreshToken; },\n/* harmony export */   login: function() { return /* binding */ login; },\n/* harmony export */   logout: function() { return /* binding */ logout; },\n/* harmony export */   refreshToken: function() { return /* binding */ refreshToken; },\n/* harmony export */   register: function() { return /* binding */ register; },\n/* harmony export */   updateCustomer: function() { return /* binding */ updateCustomer; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @swc/helpers/_/_tagged_template_literal */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_tagged_template_literal.js\");\n/* harmony import */ var graphql_request__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! graphql-request */ \"(app-pages-browser)/./node_modules/graphql-request/build/entrypoints/main.js\");\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * Client-side authentication module that works with the WooCommerce authentication API\n */ \nfunction _templateObject() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        '\\n  mutation LoginUser($username: String!, $password: String!) {\\n    login(input: {\\n      clientMutationId: \"login\"\\n      username: $username\\n      password: $password\\n    }) {\\n      authToken\\n      refreshToken\\n      user {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n      }\\n    }\\n  }\\n'\n    ]);\n    _templateObject = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject1() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RegisterUser($input: RegisterCustomerInput!) {\\n    registerCustomer(input: $input) {\\n      clientMutationId\\n      authToken\\n      refreshToken\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject1 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject2() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {\\n    refreshJwtAuthToken(input: $input) {\\n      authToken\\n    }\\n  }\\n\"\n    ]);\n    _templateObject2 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject3() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  query GetCustomer {\\n    customer {\\n      id\\n      databaseId\\n      email\\n      firstName\\n      lastName\\n      billing {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n        email\\n        phone\\n      }\\n      shipping {\\n        firstName\\n        lastName\\n        company\\n        address1\\n        address2\\n        city\\n        state\\n        postcode\\n        country\\n      }\\n      orders {\\n        nodes {\\n          id\\n          databaseId\\n          date\\n          status\\n          total\\n          lineItems {\\n            nodes {\\n              product {\\n                node {\\n                  id\\n                  name\\n                }\\n              }\\n              quantity\\n              total\\n            }\\n          }\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject3 = function() {\n        return data;\n    };\n    return data;\n}\nfunction _templateObject4() {\n    const data = (0,_swc_helpers_tagged_template_literal__WEBPACK_IMPORTED_MODULE_0__._)([\n        \"\\n  mutation UpdateCustomer($input: UpdateCustomerInput!) {\\n    updateCustomer(input: $input) {\\n      clientMutationId\\n      customer {\\n        id\\n        databaseId\\n        email\\n        firstName\\n        lastName\\n        displayName\\n        billing {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n          email\\n          phone\\n        }\\n        shipping {\\n          firstName\\n          lastName\\n          company\\n          address1\\n          address2\\n          city\\n          state\\n          postcode\\n          country\\n        }\\n      }\\n    }\\n  }\\n\"\n    ]);\n    _templateObject4 = function() {\n        return data;\n    };\n    return data;\n}\n\n\n\n// Auth token cookie name\nconst AUTH_COOKIE_NAME = \"woo_auth_token\";\nconst REFRESH_COOKIE_NAME = \"woo_refresh_token\";\n// Login mutation\nconst LOGIN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject());\n// Register mutation\nconst REGISTER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject1());\n// Refresh token mutation\nconst REFRESH_TOKEN_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject2());\n// Get customer query\nconst GET_CUSTOMER_QUERY = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject3());\n// Update customer mutation\nconst UPDATE_CUSTOMER_MUTATION = (0,graphql_request__WEBPACK_IMPORTED_MODULE_1__.gql)(_templateObject4());\nconst endpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://your-wordpress-site.com/graphql\";\nconst graphQLClient = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint);\n// Client-side cookie utilities\nfunction setCookie(name, value) {\n    let days = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30;\n    // Use SameSite=Lax for better security while allowing normal navigation\n    // Secure flag should only be used in production (HTTPS)\n    const secure =  false ? 0 : \"\";\n    const date = new Date();\n    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);\n    const expires = \"; expires=\" + date.toUTCString();\n    document.cookie = \"\".concat(name, \"=\").concat(value).concat(expires, \"; path=/; SameSite=Lax\").concat(secure);\n}\nfunction getCookie(name) {\n    if (typeof document === \"undefined\") return null;\n    const nameEQ = \"\".concat(name, \"=\");\n    const ca = document.cookie.split(\";\");\n    for(let i = 0; i < ca.length; i++){\n        let c = ca[i];\n        while(c.charAt(0) === \" \")c = c.substring(1, c.length);\n        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);\n    }\n    return null;\n}\nfunction deleteCookie(name) {\n    if (typeof document === \"undefined\") return;\n    document.cookie = \"\".concat(name, \"=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;SameSite=Lax\");\n}\n/**\n * Login user with username/email and password\n */ async function login(username, password) {\n    try {\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"login\",\n                username,\n                password\n            }),\n            credentials: \"include\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Login failed\");\n        }\n        const data = await response.json();\n        // Update auth state based on response success\n        if (data.success && data.user) {\n            // Store login time for session tracking\n            if (typeof localStorage !== \"undefined\") {\n                localStorage.setItem(\"auth_session_started\", Date.now().toString());\n            }\n            console.log(\"Login successful, user data received\");\n            return {\n                success: true,\n                user: data.user\n            };\n        } else {\n            console.error(\"Login response missing user data\");\n            throw new Error(\"Login failed: Invalid response from server\");\n        }\n    } catch (error) {\n        console.error(\"Login error:\", error);\n        return {\n            success: false,\n            message: error.message || \"Login failed\"\n        };\n    }\n}\n/**\n * Register a new user\n */ async function register(email, firstName, lastName, password) {\n    try {\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"register\",\n                email,\n                firstName,\n                lastName,\n                password\n            }),\n            credentials: \"include\"\n        });\n        if (!response.ok) {\n            const error = await response.json();\n            throw new Error(error.message || \"Registration failed\");\n        }\n        const data = await response.json();\n        // Check for success and customer data\n        if (data.success && data.customer) {\n            // Store login time for session tracking\n            if (typeof localStorage !== \"undefined\") {\n                localStorage.setItem(\"auth_session_started\", Date.now().toString());\n            }\n            console.log(\"Registration successful, user data received\");\n            return {\n                success: true,\n                customer: data.customer\n            };\n        } else {\n            console.error(\"Registration response missing customer data\");\n            throw new Error(\"Registration failed: Invalid response from server\");\n        }\n    } catch (error) {\n        console.error(\"Registration error:\", error);\n        return {\n            success: false,\n            message: error.message || \"Registration failed\"\n        };\n    }\n}\n/**\n * Logout the current user\n */ async function logout() {\n    try {\n        // Make server request to clear cookies\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"logout\"\n            }),\n            credentials: \"include\"\n        });\n        // Clear any client-side storage\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"auth_session_started\");\n        }\n        // Delete cookies client-side as well\n        deleteCookie(AUTH_COOKIE_NAME);\n        deleteCookie(REFRESH_COOKIE_NAME);\n        console.log(\"Logout successful, session cleared\");\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Logout error:\", error);\n        // Still clear local storage and cookies even if server request fails\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"auth_session_started\");\n        }\n        deleteCookie(AUTH_COOKIE_NAME);\n        deleteCookie(REFRESH_COOKIE_NAME);\n        return {\n            success: true\n        }; // Return success even if API call fails\n    }\n}\n/**\n * Check if the user is authenticated\n */ async function checkAuth() {\n    try {\n        const token = getAuthToken();\n        if (!token) {\n            return false;\n        }\n        // Check if token is expired\n        try {\n            const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_2__.jwtDecode)(token);\n            const currentTime = Date.now() / 1000;\n            if (decoded.exp < currentTime) {\n                // Token is expired, try to refresh\n                const refreshed = await refreshToken();\n                return refreshed.success;\n            }\n        } catch (e) {\n            console.error(\"Error decoding token:\", e);\n            return false;\n        }\n        // Construct absolute URL for API request\n        const baseUrl =  true ? window.location.origin : 0;\n        // Verify token with server\n        const response = await fetch(\"\".concat(baseUrl, \"/api/auth/user\"), {\n            headers: {\n                Authorization: \"Bearer \".concat(token),\n                \"Content-Type\": \"application/json\"\n            },\n            credentials: \"include\" // Include credentials (cookies)\n        });\n        if (!response.ok) {\n            console.log(\"Auth check failed with status:\", response.status);\n            return false;\n        }\n        const data = await response.json();\n        return data.success === true;\n    } catch (error) {\n        console.error(\"Auth check error:\", error);\n        return false;\n    }\n}\n/**\n * Refresh authentication token\n */ async function refreshToken() {\n    try {\n        const response = await fetch(\"/api/auth\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                action: \"refresh\"\n            })\n        });\n        const data = await response.json();\n        if (!response.ok) {\n            throw new Error(data.message || \"Token refresh failed\");\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error(\"Token refresh error:\", error);\n        return {\n            success: false,\n            message: error.message || \"Token refresh failed\"\n        };\n    }\n}\n/**\n * Get current user data\n */ async function getCurrentUser() {\n    try {\n        const response = await fetch(\"/api/auth/user\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (response.status === 401) {\n            return null;\n        }\n        const data = await response.json();\n        if (!response.ok || !data.success) {\n            return null;\n        }\n        return data.user;\n    } catch (error) {\n        console.error(\"Get user error:\", error);\n        return null;\n    }\n}\n/**\n * Get current customer data\n */ async function getCurrentCustomer() {\n    const token = getCookie(AUTH_COOKIE_NAME);\n    if (!token) {\n        return {\n            success: false,\n            message: \"Not authenticated\"\n        };\n    }\n    try {\n        // Set auth header\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        const data = await client.request(GET_CUSTOMER_QUERY);\n        if (!data.customer) {\n            throw new Error(\"Failed to fetch customer data\");\n        }\n        return {\n            success: true,\n            customer: normalizeCustomer(data.customer)\n        };\n    } catch (error) {\n        var _error_message;\n        console.error(\"Get customer error:\", error);\n        // If token expired, try to refresh\n        if ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"jwt expired\")) {\n            const refreshed = await refreshToken();\n            if (refreshed.success) {\n                return getCurrentCustomer();\n            }\n        }\n        return {\n            success: false,\n            message: error.message || \"Failed to fetch customer data\"\n        };\n    }\n}\n/**\n * Update customer data\n */ async function updateCustomer(customerData) {\n    const token = getCookie(AUTH_COOKIE_NAME);\n    if (!token) {\n        throw new Error(\"Not authenticated\");\n    }\n    try {\n        // Set auth header\n        const client = new graphql_request__WEBPACK_IMPORTED_MODULE_1__.GraphQLClient(endpoint, {\n            headers: {\n                Authorization: \"Bearer \".concat(token)\n            }\n        });\n        // Prepare input data\n        const input = {\n            clientMutationId: \"updateCustomer\",\n            firstName: customerData.firstName,\n            lastName: customerData.lastName,\n            email: customerData.email,\n            billing: customerData.billing,\n            shipping: customerData.shipping\n        };\n        const data = await client.request(UPDATE_CUSTOMER_MUTATION, {\n            input\n        });\n        if (!data.updateCustomer || !data.updateCustomer.customer) {\n            throw new Error(\"Failed to update customer data\");\n        }\n        return {\n            success: true,\n            customer: data.updateCustomer.customer\n        };\n    } catch (error) {\n        console.error(\"Update customer error:\", error);\n        throw new Error(error.message || \"Failed to update customer data\");\n    }\n}\n/**\n * Get auth token from cookies\n */ function getAuthToken() {\n    return getCookie(AUTH_COOKIE_NAME);\n}\n/**\n * Get refresh token from cookies\n */ function getRefreshToken() {\n    return getCookie(REFRESH_COOKIE_NAME);\n}\n/**\n * Normalize customer data\n */ function normalizeCustomer(customer) {\n    return {\n        id: customer.id || \"\",\n        databaseId: customer.databaseId || 0,\n        email: customer.email || \"\",\n        firstName: customer.firstName || \"\",\n        lastName: customer.lastName || \"\",\n        displayName: customer.displayName || \"\".concat(customer.firstName || \"\", \" \").concat(customer.lastName || \"\").trim(),\n        billing: customer.billing || null,\n        shipping: customer.shipping || null,\n        orders: customer.orders || null\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/clientAuth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/currency.ts":
/*!*****************************!*\
  !*** ./src/lib/currency.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_CURRENCY_CODE: function() { return /* binding */ DEFAULT_CURRENCY_CODE; },\n/* harmony export */   DEFAULT_CURRENCY_SYMBOL: function() { return /* binding */ DEFAULT_CURRENCY_SYMBOL; },\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   formatPriceWithoutSymbol: function() { return /* binding */ formatPriceWithoutSymbol; },\n/* harmony export */   getCurrencySymbol: function() { return /* binding */ getCurrencySymbol; }\n/* harmony export */ });\n/**\r\n * Currency utility functions for Ankkor\r\n */ /**\r\n * Format a numeric price to a currency string\r\n */ function formatPrice(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", currency = \"INR\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"currency\",\n        currency,\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Get currency symbol for a given currency code\r\n */ function getCurrencySymbol() {\n    let currencyCode = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"INR\", locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"en-IN\";\n    return 0..toLocaleString(locale, {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 0,\n        maximumFractionDigits: 0\n    }).replace(/\\d/g, \"\").trim();\n}\n/**\r\n * Format price without currency symbol\r\n */ function formatPriceWithoutSymbol(amount) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { locale = \"en-IN\", minimumFractionDigits = 0, maximumFractionDigits = 2 } = options;\n    const numericAmount = typeof amount === \"string\" ? parseFloat(amount) : amount;\n    return new Intl.NumberFormat(locale, {\n        style: \"decimal\",\n        minimumFractionDigits,\n        maximumFractionDigits\n    }).format(numericAmount);\n}\n/**\r\n * Default currency symbol for the application\r\n */ const DEFAULT_CURRENCY_SYMBOL = \"₹\";\nconst DEFAULT_CURRENCY_CODE = \"INR\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/currency.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/localCartStore.ts":
/*!***********************************!*\
  !*** ./src/lib/localCartStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatPrice: function() { return /* binding */ formatPrice; },\n/* harmony export */   useLocalCartItems: function() { return /* binding */ useLocalCartItems; },\n/* harmony export */   useLocalCartStore: function() { return /* binding */ useLocalCartStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/**\n * Local Cart Store for Ankkor E-commerce\n *\n * This implementation uses local storage to persist cart data on the client side.\n * When the user proceeds to checkout, the cart items are sent to WooCommerce\n * to create a server-side cart before redirecting to the checkout page.\n *\n * This approach allows for a faster, more responsive cart experience while\n * still integrating with WooCommerce's checkout process.\n */ /* __next_internal_client_entry_do_not_use__ useLocalCartStore,useLocalCartItems,formatPrice auto */ \n\n// Local storage version to handle migrations\nconst STORAGE_VERSION = 1;\n// Generate a unique ID for cart items\nconst generateItemId = ()=>{\n    return Math.random().toString(36).substring(2, 15);\n};\n// Create the store\nconst useLocalCartStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // State\n        items: [],\n        itemCount: 0,\n        isLoading: false,\n        error: null,\n        // Actions\n        addToCart: async (item)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                // Check if the item already exists in the cart\n                const existingItemIndex = items.findIndex((cartItem)=>cartItem.productId === item.productId && cartItem.variationId === item.variationId);\n                if (existingItemIndex !== -1) {\n                    // If item exists, update quantity\n                    const updatedItems = [\n                        ...items\n                    ];\n                    updatedItems[existingItemIndex].quantity += item.quantity;\n                    set({\n                        items: updatedItems,\n                        itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                        isLoading: false\n                    });\n                } else {\n                    // If item doesn't exist, add it with a new ID\n                    const newItem = {\n                        ...item,\n                        id: generateItemId()\n                    };\n                    set({\n                        items: [\n                            ...items,\n                            newItem\n                        ],\n                        itemCount: items.reduce((sum, item)=>sum + item.quantity, 0) + newItem.quantity,\n                        isLoading: false\n                    });\n                }\n                // Show success message\n                console.log(\"Item added to cart successfully\");\n            } catch (error) {\n                console.error(\"Error adding item to cart:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        updateCartItem: (id, quantity)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                if (quantity <= 0) {\n                    // If quantity is 0 or negative, remove the item\n                    return get().removeCartItem(id);\n                }\n                // Find the item and update its quantity\n                const updatedItems = items.map((item)=>item.id === id ? {\n                        ...item,\n                        quantity\n                    } : item);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error updating cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        removeCartItem: (id)=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const items = get().items;\n                const updatedItems = items.filter((item)=>item.id !== id);\n                set({\n                    items: updatedItems,\n                    itemCount: updatedItems.reduce((sum, item)=>sum + item.quantity, 0),\n                    isLoading: false\n                });\n            } catch (error) {\n                console.error(\"Error removing cart item:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n            }\n        },\n        clearCart: ()=>{\n            set({\n                items: [],\n                itemCount: 0,\n                isLoading: false,\n                error: null\n            });\n        },\n        setError: (error)=>{\n            set({\n                error\n            });\n        },\n        setIsLoading: (isLoading)=>{\n            set({\n                isLoading\n            });\n        },\n        // Helper methods\n        subtotal: ()=>{\n            const items = get().items;\n            return items.reduce((total, item)=>{\n                return total + parseFloat(item.price) * item.quantity;\n            }, 0);\n        },\n        total: ()=>{\n            // For now, total is the same as subtotal\n            // In the future, you could add shipping, tax, etc.\n            return get().subtotal();\n        },\n        // Sync cart with WooCommerce and get checkout URL\n        syncWithWooCommerce: async ()=>{\n            const items = get().items;\n            if (items.length === 0) {\n                throw new Error(\"Cart is empty\");\n            }\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const baseUrl = \"https://deepskyblue-penguin-370791.hostingersite.com\" || 0;\n                if (!baseUrl) {\n                    throw new Error(\"WooCommerce URL not configured\");\n                }\n                // For single item, use direct add-to-cart URL with checkout redirect\n                if (items.length === 1) {\n                    const item = items[0];\n                    // Create a URL that will:\n                    // 1. Add the item to cart\n                    // 2. Redirect to checkout\n                    // 3. Enable guest checkout\n                    // 4. Skip login reminder\n                    let checkoutUrl = \"\".concat(baseUrl, \"/?wc-ajax=add_to_cart\");\n                    checkoutUrl += \"&product_id=\".concat(item.productId);\n                    checkoutUrl += \"&quantity=\".concat(item.quantity);\n                    if (item.variationId) {\n                        checkoutUrl += \"&variation_id=\".concat(item.variationId);\n                    }\n                    // Add redirect to checkout parameter\n                    checkoutUrl += \"&redirect_to=\".concat(encodeURIComponent(\"\".concat(baseUrl, \"/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0\")));\n                    set({\n                        isLoading: false\n                    });\n                    return checkoutUrl;\n                }\n                // For multiple items, we need to create a proper checkout URL\n                // This approach uses the direct checkout URL with guest checkout parameters\n                const checkoutUrl = \"\".concat(baseUrl, \"/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0\");\n                // In a production environment, you should use the WooCommerce REST API\n                // to properly create a cart with multiple items\n                console.warn(\"Multiple items in cart. Using direct checkout URL. For a complete solution, implement WooCommerce REST API for full cart sync.\");\n                set({\n                    isLoading: false\n                });\n                return checkoutUrl;\n            } catch (error) {\n                console.error(\"Error syncing with WooCommerce:\", error);\n                set({\n                    error: error instanceof Error ? error.message : \"An unknown error occurred\",\n                    isLoading: false\n                });\n                return null;\n            }\n        }\n    }), {\n    name: \"ankkor-local-cart\",\n    version: STORAGE_VERSION\n}));\n// Helper hooks\nconst useLocalCartItems = ()=>useLocalCartStore((state)=>state.items);\n// Helper functions\nconst formatPrice = function(price) {\n    let currencyCode = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"INR\";\n    const amount = typeof price === \"string\" ? parseFloat(price) : price;\n    return new Intl.NumberFormat(\"en-IN\", {\n        style: \"currency\",\n        currency: currencyCode,\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    }).format(amount);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/localCartStore.ts\n"));

/***/ })

}]);