'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Minus, ShoppingBag, ArrowRight, CreditCard, AlertTriangle, Refresh<PERSON>w, WifiOff, Trash2 } from 'lucide-react';
import { useLocalCartStore } from '@/lib/localCartStore';
import type { CartItem } from '@/lib/localCartStore';
import * as woocommerce from '@/lib/woocommerce';
import Loader from '@/components/ui/loader';
// import AnimatedCheckoutButton from './AnimatedCheckoutButton';
import { DEFAULT_CURRENCY_SYMBOL } from '@/lib/currency';
import { Button } from '@/components/ui/button';

// Extended cart item with handle for navigation
interface ExtendedCartItem extends CartItem {
  productHandle?: string;
}

// Cart component props
interface CartProps {
  isOpen: boolean;
  toggleCart: () => void;
}

const Cart: React.FC<CartProps> = ({ isOpen, toggleCart }) => {
  const [checkoutLoading, setCheckoutLoading] = useState(false);
  const [checkoutError, setCheckoutError] = useState<string | null>(null);
  const [quantityUpdateInProgress, setQuantityUpdateInProgress] = useState(false);
  const [isRetrying, setIsRetrying] = useState(false);
  const [productHandles, setProductHandles] = useState<Record<string, string>>({});
  const router = useRouter();
  
  // Get cart data from the store
  const cart = useLocalCartStore();
  const { 
    items, 
    itemCount, 
    removeCartItem: removeItem, 
    updateCartItem: updateItem, 
    clearCart,
    error: initializationError,
    setError
  } = cart;
  
  // Calculate subtotal
  const subtotal = cart.subtotal().toFixed(2);
  
  const currencySymbol = '₹';
  
  // Calculate total (directly from subtotal as we removed promo code)
  const total = subtotal;
  
  // Load product handles for navigation when items change
  useEffect(() => {
    const loadProductHandles = async () => {
      const newHandles: Record<string, string> = {};
      
      for (const item of items) {
        try {
          if (!productHandles[item.productId]) {
            // Fetch product details to get the handle
            const product = await woocommerce.getProductById(item.productId);
            if (product?.slug) {
              newHandles[item.productId] = product.slug;
            }
          }
        } catch (error) {
          console.error(`Failed to load handle for product ${item.productId}:`, error);
        }
      }
      
      if (Object.keys(newHandles).length > 0) {
        setProductHandles(prev => ({ ...prev, ...newHandles }));
      }
    };
    
    loadProductHandles();
  }, [items, productHandles]);
  
  // Handle quantity updates
  const handleQuantityUpdate = async (id: string, newQuantity: number) => {
    setQuantityUpdateInProgress(true);
    
    try {
      await updateItem(id, newQuantity);
    } catch (error) {
      console.error('Error updating quantity:', error);
      setError(error instanceof Error ? error.message : 'Failed to update quantity');
    } finally {
      setQuantityUpdateInProgress(false);
    }
  };
  
  // Handle removing items
  const handleRemoveItem = async (id: string) => {
    try {
      await removeItem(id);
    } catch (error) {
      console.error('Error removing item:', error);
      setError(error instanceof Error ? error.message : 'Failed to remove item');
    }
  };
  
  // Handle checkout process
  const handleCheckout = async () => {
    setCheckoutLoading(true);
    setCheckoutError(null);

    try {
      // Validate that we have items in the cart
      if (items.length === 0) {
        throw new Error('Your cart is empty');
      }

      // Get WooCommerce checkout URL and redirect directly
      const checkoutUrl = await cart.syncWithWooCommerce();

      if (checkoutUrl) {
        // Close the cart drawer first
        toggleCart();
        // Redirect to WooCommerce checkout
        window.location.href = checkoutUrl;
      } else {
        throw new Error('Failed to get checkout URL');
      }
    } catch (error) {
      console.error('Checkout error:', error);
      setCheckoutError(error instanceof Error ? error.message : 'An error occurred during checkout');
      setCheckoutLoading(false);
    }
  };
  
  // Handle retry for errors
  const handleRetry = async () => {
    setIsRetrying(true);
    setCheckoutError(null);
    
    try {
      // Retry the checkout process
      await handleCheckout();
    } catch (error) {
      console.error('Retry error:', error);
      setCheckoutError(error instanceof Error ? error.message : 'Retry failed');
    } finally {
      setIsRetrying(false);
    }
  };
  
  // Get fallback image URL
  const getImageUrl = (item: CartItem) => {
    return item.image?.url || '/placeholder-product.jpg';
  };
  
  const hasItems = items.length > 0;

  return (
    <>
      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleCart}
            className="fixed inset-0 bg-black/50 z-40"
            aria-hidden="true"
          />
        )}
      </AnimatePresence>

      {/* Cart Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'tween', ease: 'easeInOut', duration: 0.3 }}
            className="fixed top-0 right-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col"
          >
            {/* Cart Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-medium flex items-center gap-2">
                <ShoppingBag className="h-5 w-5" />
                Your Cart
              </h2>
              <button
                onClick={toggleCart}
                className="p-2 hover:bg-gray-100 rounded-full"
                aria-label="Close cart"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Cart Content */}
            <div className="flex-1 overflow-y-auto p-4">
              {/* Empty Cart State */}
              {!hasItems && !initializationError && (
                <div className="flex flex-col items-center justify-center h-full text-center p-4">
                  <ShoppingBag className="h-12 w-12 text-gray-300 mb-2" />
                  <h3 className="text-lg font-medium mb-1">Your cart is empty</h3>
                  <p className="text-gray-500 mb-4">Looks like you haven't added any items yet.</p>
                  <Button 
                    onClick={toggleCart}
                    className="flex items-center gap-2"
                  >
                    Continue Shopping
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              )}

              {/* Error State */}
              {initializationError && (
                <div className="flex flex-col items-center justify-center h-full text-center p-4">
                  <AlertTriangle className="h-12 w-12 text-red-500 mb-2" />
                  <h3 className="text-lg font-medium mb-1">Something went wrong</h3>
                  <p className="text-gray-500 mb-4">{initializationError}</p>
                  <Button 
                    onClick={() => setError(null)}
                    className="flex items-center gap-2"
                    variant="outline"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Try Again
                  </Button>
                </div>
              )}

              {/* Cart Items */}
              {hasItems && (
                <ul className="divide-y">
                  {items.map(item => (
                    <CartItem
                      key={item.id}
                      item={item}
                      updateQuantity={handleQuantityUpdate}
                      removeFromCart={handleRemoveItem}
                    />
                  ))}
                </ul>
              )}
            </div>

            {/* Cart Footer */}
            {hasItems && (
              <div className="p-4 border-t">
                {/* Subtotal */}
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="font-medium">{currencySymbol}{subtotal}</span>
                </div>

                {/* Total */}
                <div className="flex justify-between mb-4">
                  <span className="font-medium">Total</span>
                  <span className="font-medium">{currencySymbol}{total}</span>
                </div>

                {/* Checkout Error */}
                {checkoutError && (
                  <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium">Checkout Error</p>
                      <p className="text-sm">{checkoutError}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={handleRetry}
                        disabled={isRetrying}
                      >
                        {isRetrying && <RefreshCw className="h-3 w-3 mr-2 animate-spin" />}
                        Try Again
                      </Button>
                    </div>
                  </div>
                )}

                {/* Checkout Button */}
                <AnimatedCheckoutButton
                  onClick={handleCheckout}
                  isLoading={checkoutLoading}
                  disabled={checkoutLoading || quantityUpdateInProgress}
                />

                {/* Clear Cart Button */}
                <button
                  onClick={clearCart}
                  className="w-full text-center text-gray-500 text-sm mt-2 hover:text-gray-700"
                  disabled={checkoutLoading || quantityUpdateInProgress}
                >
                  Clear Cart
                </button>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

interface CartItemProps {
  item: CartItem;
  updateQuantity: (id: string, quantity: number) => void;
  removeFromCart: (id: string) => void;
}

const CartItem: React.FC<CartItemProps> = ({ item, updateQuantity, removeFromCart }) => {
  const handleIncrement = () => {
    updateQuantity(item.id, item.quantity + 1);
  };

  const handleDecrement = () => {
    if (item.quantity > 1) {
      updateQuantity(item.id, item.quantity - 1);
    }
  };

  const handleRemove = () => {
    removeFromCart(item.id);
  };

  return (
    <li className="flex gap-4 py-4 border-b">
      {/* Product Image */}
      <div className="relative h-20 w-20 bg-gray-100 flex-shrink-0">
        {item.image?.url && (
          <Image
            src={item.image.url}
            alt={item.image.altText || item.name}
            fill
            sizes="80px"
            className="object-cover"
            priority={false}
          />
        )}
      </div>

      {/* Product Info */}
      <div className="flex-1 flex flex-col">
        <h4 className="text-sm font-medium line-clamp-2">{item.name}</h4>
        
        {/* Attributes */}
        {item.attributes && item.attributes.length > 0 && (
          <div className="mt-1 text-xs text-gray-500">
            {item.attributes.map((attr, index) => (
              <span key={attr.name}>
                {attr.name}: {attr.value}
                {index < item.attributes!.length - 1 ? ', ' : ''}
              </span>
            ))}
          </div>
        )}
        
        {/* Price */}
        <div className="mt-1 text-sm font-medium">
          {item.price.toString().includes('₹') ? item.price : `${DEFAULT_CURRENCY_SYMBOL}${parseFloat(item.price).toFixed(2)}`}
        </div>

        {/* Quantity Controls */}
        <div className="mt-2 flex items-center gap-2">
          <div className="flex items-center border border-gray-300">
            <button
              onClick={handleDecrement}
              disabled={item.quantity <= 1}
              className="px-2 py-1 hover:bg-gray-100 disabled:opacity-50"
              aria-label="Decrease quantity"
            >
              <Minus className="h-3 w-3" />
            </button>
            <span className="px-2 py-1 text-sm">{item.quantity}</span>
            <button
              onClick={handleIncrement}
              className="px-2 py-1 hover:bg-gray-100"
              aria-label="Increase quantity"
            >
              <Plus className="h-3 w-3" />
            </button>
          </div>
          
          {/* Remove Button */}
          <button
            onClick={handleRemove}
            className="p-1 hover:bg-gray-100 rounded-full"
            aria-label="Remove item"
          >
            <Trash2 className="h-4 w-4 text-gray-500" />
          </button>
        </div>
      </div>
    </li>
  );
};

export default Cart; 