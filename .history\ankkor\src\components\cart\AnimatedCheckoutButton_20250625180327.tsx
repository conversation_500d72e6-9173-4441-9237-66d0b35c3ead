'use client';

import React from 'react';
import styled from 'styled-components';
import { CreditCard } from 'lucide-react';
import Loader from '@/components/ui/loader';

interface AnimatedCheckoutButtonProps {
  onClick: () => void;
  isLoading: boolean;
  disabled: boolean;
}

const AnimatedCheckoutButton: React.FC<AnimatedCheckoutButtonProps> = ({ onClick, isLoading, disabled }) => {
  return (
    <StyledWrapper>
      <button 
        className="container"
        onClick={onClick}
        disabled={disabled || isLoading}
        aria-label="Proceed to checkout"
      >
        <div className="left-side">
          <div className="card">
            <div className="card-line" />
            <div className="buttons" />
          </div>
          <div className="post">
            <div className="post-line" />
            <div className="screen">
              <div className="dollar">₹</div>
            </div>
            <div className="numbers" />
            <div className="numbers-line2" />
          </div>
        </div>
        <div className="right-side">
          {isLoading ? (
            <div className="new flex items-center justify-center">
              <Loader className="mr-2" />
              <span>Processing...</span>
            </div>
          ) : (
            <div className="new flex items-center">
              <CreditCard className="h-4 w-4 mr-2" />
              <span>Checkout</span>
            </div>
          )}
        </div>
      </button>
    </StyledWrapper>
  );
}

const StyledWrapper = styled.div`
  width: 100%;
  
  .container {
    background-color: #ffffff;
    display: flex;
    width: 100%;
    height: 60px;
    position: relative;
    border-radius: 4px;
    transition: 0.3s ease-in-out;
    border: 1px solid #e5e2d9;
    cursor: pointer;
    outline: none;
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  .container:hover {
    transform: scale(1.02);
  }

  .container:hover .left-side {
    width: 100px;
  }

  .left-side {
    background-color: #5de2a3;
    width: 70px;
    height: 100%;
    border-radius: 4px 0 0 4px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: 0.3s;
    flex-shrink: 0;
    overflow: hidden;
  }

  .right-side {
    display: flex;
    align-items: center;
    overflow: hidden;
    justify-content: center;
    width: 100%;
    transition: 0.3s;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    font-size: 14px;
    font-weight: 500;
    color: #2c2c27;
  }

  .container:hover .right-side {
    background-color: #f4f3f0;
  }

  .card {
    width: 40px;
    height: 30px;
    background-color: #c7ffbc;
    border-radius: 4px;
    position: absolute;
    display: flex;
    z-index: 10;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 5px 10px -2px rgba(77, 200, 143, 0.5);
  }

  .card-line {
    width: 35px;
    height: 8px;
    background-color: #80ea69;
    border-radius: 2px;
    margin-top: 5px;
  }

  .buttons {
    width: 5px;
    height: 5px;
    background-color: #379e1f;
    box-shadow: 0 -8px 0 0 #26850e, 0 8px 0 0 #56be3e;
    border-radius: 50%;
    margin-top: 5px;
    transform: rotate(90deg);
    margin: 5px 0 0 -18px;
  }

  .container:hover .card {
    animation: slide-top 1.2s cubic-bezier(0.645, 0.045, 0.355, 1) both;
  }

  .container:hover .post {
    animation: slide-post 1s cubic-bezier(0.165, 0.84, 0.44, 1) both;
  }

  @keyframes slide-top {
    0% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-40px) rotate(90deg);
    }
    60% {
      transform: translateY(-40px) rotate(90deg);
    }
    100% {
      transform: translateY(-8px) rotate(90deg);
    }
  }

  .post {
    width: 40px;
    height: 45px;
    background-color: #dddde0;
    position: absolute;
    z-index: 11;
    bottom: 10px;
    top: 60px;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 5px 10px -2px rgba(77, 200, 143, 0.5);
    border: 1px solid #e5e2d9;
  }

  .post-line {
    width: 30px;
    height: 6px;
    background-color: #545354;
    position: absolute;
    border-radius: 0 0 2px 2px;
    right: 5px;
    top: 5px;
  }

  .post-line:before {
    content: "";
    position: absolute;
    width: 30px;
    height: 6px;
    background-color: #757375;
    top: -6px;
  }

  .screen {
    width: 30px;
    height: 15px;
    background-color: #ffffff;
    position: absolute;
    top: 15px;
    right: 5px;
    border-radius: 2px;
    border: 1px solid #e5e2d9;
  }

  .numbers {
    width: 8px;
    height: 8px;
    background-color: #838183;
    box-shadow: 0 -12px 0 0 #838183, 0 12px 0 0 #838183;
    border-radius: 2px;
    position: absolute;
    transform: rotate(90deg);
    left: 16px;
    top: 37px;
  }

  .numbers-line2 {
    width: 8px;
    height: 8px;
    background-color: #aaa9ab;
    box-shadow: 0 -12px 0 0 #aaa9ab, 0 12px 0 0 #aaa9ab;
    border-radius: 2px;
    position: absolute;
    transform: rotate(90deg);
    left: 16px;
    top: 48px;
  }

  @keyframes slide-post {
    50% {
      transform: translateY(0);
    }
    100% {
      transform: translateY(-40px);
    }
  }

  .dollar {
    position: absolute;
    font-size: 14px;
    width: 100%;
    left: 0;
    top: 0;
    color: #4b953b;
    text-align: center;
    font-weight: 700;
  }

  .container:hover .dollar {
    animation: fade-in-fwd 0.3s 1s backwards;
  }

  @keyframes fade-in-fwd {
    0% {
      opacity: 0;
      transform: translateY(-5px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @media only screen and (max-width: 480px) {
    .container {
      height: 50px;
    }
    
    .left-side {
      width: 60px;
    }
    
    .container:hover .left-side {
      width: 80px;
    }
    
    .card {
      width: 35px;
      height: 25px;
    }
    
    .card-line {
      width: 30px;
      height: 6px;
    }
    
    .right-side .new {
      font-size: 13px;
    }
  }
`;

export default AnimatedCheckoutButton; 