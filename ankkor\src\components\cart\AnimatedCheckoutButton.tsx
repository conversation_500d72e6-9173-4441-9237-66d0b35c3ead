'use client';

import React from 'react';
import { CreditCard } from 'lucide-react';
import Loader from '@/components/ui/loader';

interface AnimatedCheckoutButtonProps {
  onClick: () => void;
  isLoading: boolean;
  disabled: boolean;
}

const AnimatedCheckoutButton: React.FC<AnimatedCheckoutButtonProps> = ({ onClick, isLoading, disabled }) => {
  return (
    <button
      className={`
        w-full h-14 bg-white border border-gray-200 rounded-md flex items-center
        transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-lg
        ${disabled || isLoading ? 'opacity-70 cursor-not-allowed pointer-events-none' : 'cursor-pointer'}
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
      `}
      onClick={onClick}
      disabled={disabled || isLoading}
      aria-label="Proceed to checkout"
    >
      {/* Left side with animated elements */}
      <div className="w-16 h-full bg-green-400 rounded-l-md flex items-center justify-center relative overflow-hidden transition-all duration-300 hover:w-20">
        {/* Card animation */}
        <div className="w-8 h-6 bg-green-200 rounded absolute z-10 shadow-lg">
          <div className="w-7 h-2 bg-green-500 rounded-sm mt-1 mx-auto"></div>
          <div className="w-1 h-1 bg-green-700 rounded-full mt-1 ml-1"></div>
        </div>

        {/* ATM/Terminal animation */}
        <div className="w-8 h-9 bg-gray-300 rounded absolute top-12 border border-gray-400 shadow-lg">
          <div className="w-6 h-1 bg-gray-600 rounded-b-sm absolute right-1 top-1"></div>
          <div className="w-6 h-3 bg-white rounded-sm absolute right-1 top-3 border border-gray-300 flex items-center justify-center">
            <span className="text-xs font-bold text-green-600">₹</span>
          </div>
          <div className="w-2 h-2 bg-gray-500 rounded-sm absolute left-3 bottom-2"></div>
        </div>
      </div>

      {/* Right side with text */}
      <div className="flex-1 flex items-center justify-center text-gray-800 font-medium uppercase tracking-wide text-sm transition-colors hover:bg-gray-50">
        {isLoading ? (
          <div className="flex items-center justify-center">
            <Loader className="mr-2" />
            <span>Processing...</span>
          </div>
        ) : (
          <div className="flex items-center">
            <CreditCard className="h-4 w-4 mr-2" />
            <span>Checkout</span>
          </div>
        )}
      </div>
    </button>
  );
}

export default AnimatedCheckoutButton;