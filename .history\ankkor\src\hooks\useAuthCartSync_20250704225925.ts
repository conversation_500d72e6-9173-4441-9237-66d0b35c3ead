import { useEffect, useRef, useState } from 'react';
import { useCustomer } from '@/components/providers/CustomerProvider';
import { useCartStore, useWishlistStore } from '@/lib/store';

/**
 * Hook to synchronize authentication state with cart and wishlist state.
 * Ensures cart is properly handled when user signs in or out, and manages
 * wishlist synchronization between guest and authenticated states.
 */
export function useAuthCartSync() {
  const { isAuthenticated, customer } = useCustomer();
  const cartStore = useCartStore();
  const wishlistStore = useWishlistStore();
  const [isSyncing, setIsSyncing] = useState(false);
  
  // Use refs to track previous authentication state and customer ID
  const prevAuthRef = useRef(isAuthenticated);
  const prevCustomerIdRef = useRef(customer?.id || null);
  
  // Effect to handle auth state changes
  useEffect(() => {
    // Skip if already syncing to prevent loops
    if (isSyncing) return;
    
    // Function to clear cart and wishlist data on logout
    const handleLogout = async () => {
      setIsSyncing(true);
      console.log('Auth state changed: User logged out - resetting cart and wishlist');
      
      try {
        // Clear cart
        await cartStore.clearCart();
        
        // Store current wishlist items in session storage for potential recovery
        if (typeof window !== 'undefined' && wishlistStore.items.length > 0) {
          try {
            sessionStorage.setItem(
              'ankkor_temp_wishlist', 
              JSON.stringify(wishlistStore.items)
            );
            console.log('Saved wishlist items to session storage for recovery');
          } catch (error) {
            console.error('Failed to save wishlist to session storage:', error);
          }
        }
        
        // Clear any indicators in session storage
        if (typeof window !== 'undefined') {
          sessionStorage.removeItem('cartInitialized');
        }
        
        // Re-initialize the cart
        await cartStore.initCart();
        
        // Mark as initialized again
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('cartInitialized', 'true');
        }
      } catch (error) {
        console.error('Error handling logout:', error);
      } finally {
        setIsSyncing(false);
      }
    };
    
    // Function to handle login and merge guest cart/wishlist with account
    const handleLogin = async () => {
      setIsSyncing(true);
      console.log('Auth state changed: User logged in - syncing cart and wishlist');
      
      try {
        // Cart sync happens automatically through Shopify's API when authenticated
        
        // Show a tooltip or notification that the cart was transferred if needed
        // This could integrate with a toast/notification system
        
        // Sync wishlist with user profile
        await syncWishlistOnLogin();
      } catch (error) {
        console.error('Error handling login:', error);
      } finally {
        setIsSyncing(false);
      }
    };
    
    // Check if auth state changed
    if (prevAuthRef.current !== isAuthenticated) {
      if (isAuthenticated) {
        // User just logged in
        handleLogin();
      } else {
        // User just logged out
        handleLogout();
      }
    } else if (isAuthenticated && customer?.id !== prevCustomerIdRef.current) {
      // User switched accounts while staying logged in
      handleLogin();
    }
    
    // Update the refs for the next render
    prevAuthRef.current = isAuthenticated;
    prevCustomerIdRef.current = customer?.id || null;
    
  }, [isAuthenticated, customer?.id, cartStore, wishlistStore, isSyncing]);
  
  return null;
} 