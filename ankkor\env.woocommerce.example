# WordPress/WooCommerce Configuration
NEXT_PUBLIC_WORDPRESS_URL=lightpink-eagle-376738.hostingersite.com
WOOCOMMERCE_GRAPHQL_URL=lightpink-eagle-376738.hostingersite.com/graphql
WOOCOMMERCE_CONSUMER_KEY=ck_4d17fdf284e132b57f8a5ff57c1a1897473ac27e
WOOCOMMERCE_CONSUMER_SECRET=cs_bf0f11b174f675ac0a690b15fe0e486302d9d2f3
WOOCOMMERCE_JWT_SECRET=your-jwt-secret-key
WOOCOMMERCE_REVALIDATION_SECRET=your-revalidation-secret
WOOCOMMERCE_WEBHOOK_SECRET=your-webhook-secret

# Redis Configuration for Caching and Inventory Mapping
UPSTASH_REDIS_REST_URL=https://fast-crayfish-46451.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-redis-token

# QStash Configuration for Background Jobs
QSTASH_TOKEN=your-qstash-token

# Commerce Provider - Set to woocommerce
NEXT_PUBLIC_COMMERCE_PROVIDER=woocommerce

# Test User Credentials (For Validation Tests)
TEST_USER_EMAIL=
TEST_USER_PASSWORD=

# WooCommerce REST API (if needed)
WOOCOMMERCE_REST_API_URL=https://your-wordpress-site.com/wp-json/wc/v3

# Stripe Payment Gateway
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# PayPal Payment Gateway (if needed)
NEXT_PUBLIC_PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret

# Next.js
NEXT_PUBLIC_SITE_URL=https://your-site.com
NODE_ENV=development # or production 