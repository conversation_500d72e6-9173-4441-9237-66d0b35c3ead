/**
 * Local Cart Store for Ankkor E-commerce
 *
 * This implementation uses local storage to persist cart data on the client side.
 * When the user proceeds to checkout, the cart items are sent to WooCommerce
 * to create a server-side cart before redirecting to the checkout page.
 *
 * This approach allows for a faster, more responsive cart experience while
 * still integrating with WooCommerce's checkout process.
 */

'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import * as woocommerce from '@/lib/woocommerce';

// Type definitions
export interface CartItem {
  id: string;
  productId: string;
  variationId?: string;
  quantity: number;
  name: string;
  price: string;
  image?: {
    url: string;
    altText?: string;
  };
  attributes?: Array<{
    name: string;
    value: string;
  }>;
}

export interface LocalCart {
  items: CartItem[];
  itemCount: number;
  isLoading: boolean;
  error: string | null;
}

// Actions interface
interface CartActions {
  addToCart: (item: Omit<CartItem, 'id'>) => Promise<void>;
  updateCartItem: (id: string, quantity: number) => void;
  removeCartItem: (id: string) => void;
  clearCart: () => void;
  setError: (error: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  syncWithWooCommerce: () => Promise<string | null>; // Returns checkout URL
}

// Cart store interface
export interface LocalCartStore extends LocalCart, CartActions {
  subtotal: () => number;
  total: () => number;
}

// Local storage version to handle migrations
const STORAGE_VERSION = 1;

// Generate a unique ID for cart items
const generateItemId = (): string => {
  return Math.random().toString(36).substring(2, 15);
};

// Create the store
export const useLocalCartStore = create<LocalCartStore>()(
  persist(
    (set, get) => ({
      // State
      items: [],
      itemCount: 0,
      isLoading: false,
      error: null,

      // Actions
      addToCart: async (item) => {
        set({ isLoading: true, error: null });

        try {
          const items = get().items;

          // Check if the item already exists in the cart
          const existingItemIndex = items.findIndex(
            (cartItem) =>
              cartItem.productId === item.productId &&
              cartItem.variationId === item.variationId
          );

          if (existingItemIndex !== -1) {
            // If item exists, update quantity
            const updatedItems = [...items];
            updatedItems[existingItemIndex].quantity += item.quantity;

            set({
              items: updatedItems,
              itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
              isLoading: false,
            });
          } else {
            // If item doesn't exist, add it with a new ID
            const newItem = {
              ...item,
              id: generateItemId(),
            };

            set({
              items: [...items, newItem],
              itemCount: items.reduce((sum, item) => sum + item.quantity, 0) + newItem.quantity,
              isLoading: false,
            });
          }

          // Show success message
          console.log('Item added to cart successfully');
        } catch (error) {
          console.error('Error adding item to cart:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      updateCartItem: (id, quantity) => {
        set({ isLoading: true, error: null });
        
        try {
          const items = get().items;
          
          if (quantity <= 0) {
            // If quantity is 0 or negative, remove the item
            return get().removeCartItem(id);
          }
          
          // Find the item and update its quantity
          const updatedItems = items.map(item => 
            item.id === id ? { ...item, quantity } : item
          );
          
          set({
            items: updatedItems,
            itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
            isLoading: false,
          });
        } catch (error) {
          console.error('Error updating cart item:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      removeCartItem: (id) => {
        set({ isLoading: true, error: null });
        
        try {
          const items = get().items;
          const updatedItems = items.filter(item => item.id !== id);
          
          set({
            items: updatedItems,
            itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
            isLoading: false,
          });
        } catch (error) {
          console.error('Error removing cart item:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      clearCart: () => {
        set({
          items: [],
          itemCount: 0,
          isLoading: false,
          error: null,
        });
      },

      setError: (error) => {
        set({ error });
      },

      setIsLoading: (isLoading) => {
        set({ isLoading });
      },

      // Helper methods
      subtotal: () => {
        const items = get().items;
        return items.reduce((total, item) => {
          return total + (parseFloat(item.price) * item.quantity);
        }, 0);
      },

      total: () => {
        // For now, total is the same as subtotal
        // In the future, you could add shipping, tax, etc.
        return get().subtotal();
      },

      // Sync cart with WooCommerce and get checkout URL
      syncWithWooCommerce: async () => {
        const items = get().items;
        if (items.length === 0) {
          throw new Error('Cart is empty');
        }

        set({ isLoading: true, error: null });

        try {
          const baseUrl = process.env.NEXT_PUBLIC_WOOCOMMERCE_URL || process.env.NEXT_PUBLIC_BACKEND_URL;
          if (!baseUrl) {
            throw new Error('WooCommerce URL not configured');
          }

          // For single item, use direct add-to-cart URL with checkout redirect
          if (items.length === 1) {
            const item = items[0];
            
            // Create a URL that will:
            // 1. Add the item to cart
            // 2. Redirect to checkout
            // 3. Enable guest checkout
            // 4. Skip login reminder
            let checkoutUrl = `${baseUrl}/?wc-ajax=add_to_cart`;
            checkoutUrl += `&product_id=${item.productId}`;
            checkoutUrl += `&quantity=${item.quantity}`;
            
            if (item.variationId) {
              checkoutUrl += `&variation_id=${item.variationId}`;
            }
            
            // Add redirect to checkout parameter
            checkoutUrl += `&redirect_to=${encodeURIComponent(`${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0`)}`;
            
            set({ isLoading: false });
            return checkoutUrl;
          }

          // For multiple items, we need to create a proper checkout URL
          // This approach uses the direct checkout URL with guest checkout parameters
          const checkoutUrl = `${baseUrl}/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0`;
          
          // In a production environment, you should use the WooCommerce REST API
          // to properly create a cart with multiple items
          console.warn('Multiple items in cart. Using direct checkout URL. For a complete solution, implement WooCommerce REST API for full cart sync.');
          
          set({ isLoading: false });
          return checkoutUrl;
        } catch (error) {
          console.error('Error syncing with WooCommerce:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
          return null;
        }
      }
    }),
    {
      name: 'ankkor-local-cart',
      version: STORAGE_VERSION,
    }
  )
);

// Helper hooks
export const useLocalCartItems = () => useLocalCartStore(state => state.items);

// Helper functions
export const formatPrice = (price: string | number, currencyCode = 'INR') => {
  const amount = typeof price === 'string' ? parseFloat(price) : price;

  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};