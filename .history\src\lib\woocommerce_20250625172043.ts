import { gql } from 'graphql-request';

// Product query using fragments for different product types
export const GET_PRODUCTS = gql`
  query GetProducts($first: Int, $after: String, $where: RootQueryToProductConnectionWhereArgs) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        price
        regularPrice
        salePrice
        onSale
        image {
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            sourceUrl
            altText
          }
        }
        ... on SimpleProduct {
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          stockStatus
          stockQuantity
          productCategories {
            nodes {
              name
              slug
            }
          }
        }
      }
    }
  }
`;

// Get a single product with variations if it's a variable product
export const GET_PRODUCT = gql`
  query GetProduct($id: ID!) {
    product(id: $id, idType: SLUG) {
      id
      databaseId
      name
      slug
      description
      shortDescription
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      image {
        sourceUrl
        altText
      }
      galleryImages {
        nodes {
          sourceUrl
          altText
        }
      }
      ... on SimpleProduct {
        stockStatus
        stockQuantity
      }
      ... on VariableProduct {
        attributes {
          nodes {
            name
            options
            variation
          }
        }
        defaultAttributes {
          nodes {
            name
            value
          }
        }
        variations {
          nodes {
            id
            databaseId
            name
            price
            regularPrice
            salePrice
            stockStatus
            stockQuantity
            attributes {
              nodes {
                name
                value
              }
            }
          }
        }
      }
    }
  }
`;

// Cart query without the totalShipping field that's causing errors
export const GET_CART = gql`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              name
              slug
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`;

// Add to cart mutation
export const ADD_TO_CART = gql`
  mutation AddToCart($input: AddToCartInput!) {
    addToCart(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
      }
    }
  }
`;

// Update cart item quantities
export const UPDATE_CART_ITEM = gql`
  mutation UpdateCartItems($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                name
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
      }
    }
  }
`;

// Remove items from cart
export const REMOVE_ITEMS_FROM_CART = gql`
  mutation RemoveItemsFromCart($input: RemoveItemsFromCartInput!) {
    removeItemsFromCart(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                name
              }
            }
            quantity
          }
        }
        subtotal
        total
        isEmpty
      }
    }
  }
`; 