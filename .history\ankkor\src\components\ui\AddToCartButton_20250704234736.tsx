import React from 'react';
import { ShoppingBag } from 'lucide-react';

interface AddToCartButtonProps {
  onClick: (e: React.MouseEvent) => void;
  disabled?: boolean;
  isLoading?: boolean;
  className?: string;
}

const AddToCartButton: React.FC<AddToCartButtonProps> = ({
  onClick,
  disabled = false,
  isLoading = false,
  className = ""
}) => {
  return (
    <div className="relative">
      <button
        onClick={onClick}
        disabled={disabled || isLoading}
        className={`
          group
          w-full h-10
          rounded-none
          border-none
          bg-[#2c2c27]
          hover:bg-[#3d3d35]
          flex items-center justify-center
          cursor-pointer
          transition-all duration-500
          overflow-hidden
          shadow-sm hover:shadow-md
          relative
          disabled:bg-gray-400 disabled:cursor-not-allowed
          disabled:hover:bg-gray-400
          active:scale-95
          ${className}
        `}
      >
        <div className="
          absolute -left-12
          w-8 h-8
          bg-transparent
          rounded-full
          flex items-center justify-center
          overflow-hidden
          z-10
          transition-all duration-500
          group-hover:translate-x-14 group-hover:rounded-lg
          group-disabled:group-hover:translate-x-0
        ">
          <ShoppingBag
            className="h-4 w-4 text-[#f4f3f0]"
            fill="currentColor"
          />
        </div>

        <span className="
          h-full w-fit
          flex items-center justify-center
          text-[#f4f3f0]
          z-0
          transition-all duration-500
          text-sm font-medium tracking-wider uppercase
          group-hover:translate-x-2
          group-disabled:group-hover:translate-x-0
        ">
          {disabled ? 'Out of Stock' : isLoading ? 'Adding...' : 'Add to Cart'}
        </span>
      </button>
    </div>
  );
};

export default AddToCartButton;
