/**
 * Local Cart Store for Ankkor E-commerce
 * 
 * This implementation uses local storage to persist cart data on the client side.
 * When the user proceeds to checkout, the cart items are sent to WooCommerce
 * to create a server-side cart before redirecting to the checkout page.
 * 
 * This approach allows for a faster, more responsive cart experience while
 * still integrating with WooCommerce's checkout process.
 */

'use client';

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// Type definitions
export interface CartItem {
  id: string;
  productId: string;
  variationId?: string;
  quantity: number;
  name: string;
  price: string;
  image?: {
    url: string;
    altText?: string;
  };
  attributes?: Array<{
    name: string;
    value: string;
  }>;
}

export interface LocalCart {
  items: CartItem[];
  itemCount: number;
  isLoading: boolean;
  error: string | null;
}

// Actions interface
interface CartActions {
  addToCart: (item: Omit<CartItem, 'id'>) => void;
  updateCartItem: (id: string, quantity: number) => void;
  removeCartItem: (id: string) => void;
  clearCart: () => void;
  setError: (error: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
}

// Cart store interface
export interface LocalCartStore extends LocalCart, CartActions {
  subtotal: () => number;
  total: () => number;
}

// Local storage version to handle migrations
const STORAGE_VERSION = 1;

// Generate a unique ID for cart items
const generateItemId = (): string => {
  return Math.random().toString(36).substring(2, 15);
};

// Create the store
export const useLocalCartStore = create<LocalCartStore>()(
  persist(
    (set, get) => ({
      // State
      items: [],
      itemCount: 0,
      isLoading: false,
      error: null,

      // Actions
      addToCart: (item) => {
        set({ isLoading: true, error: null });
        
        try {
          const items = get().items;
          
          // Check if the item already exists in the cart
          const existingItemIndex = items.findIndex(
            (cartItem) => 
              cartItem.productId === item.productId && 
              cartItem.variationId === item.variationId
          );
          
          if (existingItemIndex !== -1) {
            // If item exists, update quantity
            const updatedItems = [...items];
            updatedItems[existingItemIndex].quantity += item.quantity;
            
            set({
              items: updatedItems,
              itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
              isLoading: false,
            });
          } else {
            // If item doesn't exist, add it with a new ID
            const newItem = {
              ...item,
              id: generateItemId(),
            };
            
            set({
              items: [...items, newItem],
              itemCount: items.reduce((sum, item) => sum + item.quantity, 0) + newItem.quantity,
              isLoading: false,
            });
          }
        } catch (error) {
          console.error('Error adding item to cart:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      updateCartItem: (id, quantity) => {
        set({ isLoading: true, error: null });
        
        try {
          const items = get().items;
          
          if (quantity <= 0) {
            // If quantity is 0 or negative, remove the item
            return get().removeCartItem(id);
          }
          
          // Find the item and update its quantity
          const updatedItems = items.map(item => 
            item.id === id ? { ...item, quantity } : item
          );
          
          set({
            items: updatedItems,
            itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
            isLoading: false,
          });
        } catch (error) {
          console.error('Error updating cart item:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      removeCartItem: (id) => {
        set({ isLoading: true, error: null });
        
        try {
          const items = get().items;
          const updatedItems = items.filter(item => item.id !== id);
          
          set({
            items: updatedItems,
            itemCount: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
            isLoading: false,
          });
        } catch (error) {
          console.error('Error removing cart item:', error);
          set({
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            isLoading: false,
          });
        }
      },

      clearCart: () => {
        set({
          items: [],
          itemCount: 0,
          isLoading: false,
          error: null,
        });
      },

      setError: (error) => {
        set({ error });
      },

      setIsLoading: (isLoading) => {
        set({ isLoading });
      },

      // Helper methods
      subtotal: () => {
        const items = get().items;
        return items.reduce((total, item) => {
          return total + (parseFloat(item.price) * item.quantity);
        }, 0);
      },

      total: () => {
        // For now, total is the same as subtotal
        // In the future, you could add shipping, tax, etc.
        return get().subtotal();
      }
    }),
    {
      name: 'ankkor-local-cart',
      version: STORAGE_VERSION,
    }
  )
);

// Helper hooks
export const useLocalCartItems = () => useLocalCartStore(state => state.items);

// Helper functions
export const formatPrice = (price: string | number, currencyCode = 'INR') => {
  const amount = typeof price === 'string' ? parseFloat(price) : price;

  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};