import React from 'react';
import styled from 'styled-components';
import Link from 'next/link';

interface HeritageButtonProps {
  href: string;
}

const HeritageButton: React.FC<HeritageButtonProps> = ({ href }) => {
  return (
    <StyledWrapper>
      <Link href={href} className="btn-wrapper">
        <button className="btn">
          Read more 
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" height="15px" width="15px" className="icon">
            <path strokeLinejoin="round" strokeLinecap="round" strokeMiterlimit={10} strokeWidth="1.5" stroke="#292D32" d="M8.91016 19.9201L15.4302 13.4001C16.2002 12.6301 16.2002 11.3701 15.4302 10.6001L8.91016 4.08008" />
          </svg>
        </button>
      </Link>
    </StyledWrapper>
  );
}

const StyledWrapper = styled.div`
  .btn {
    width: 140px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: none;
    position: relative;
    background-color: transparent;
    transition: .2s cubic-bezier(0.19, 1, 0.22, 1);
    opacity: 0.6;
  }

  .btn::after {
    content: '';
    border-bottom: 3px double rgb(214, 207, 113);
    width: 0;
    height: 100%;
    position: absolute;
    margin-top: -5px;
    top: 0;
    left: 5px;
    visibility: hidden;
    opacity: 1;
    transition: .2s linear;
  }

  .btn .icon {
    transform: translateX(0%);
    transition: .2s linear;
    animation: attention 1.2s linear infinite;
  }

  .btn:hover::after {
    visibility: visible;
    opacity: 0.7;
    width: 90%;
  }

  .btn:hover {
    letter-spacing: 2px;
    opacity: 1;
  }

  .btn:hover > .icon {
    transform: translateX(30%);
    animation: none;
  }

  @keyframes attention {
    0% {
      transform: translateX(0%);
    }

    50% {
      transform: translateX(30%);
    }
  }

  .btn-wrapper {
    text-decoration: none;
    color: inherit;
  }
`;

export default HeritageButton; 