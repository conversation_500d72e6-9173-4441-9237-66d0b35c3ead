"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_components_LaunchingStateServer_tsx";
exports.ids = ["_rsc_src_components_LaunchingStateServer_tsx"];
exports.modules = {

/***/ "(rsc)/./src/components/LaunchingStateServer.tsx":
/*!*************************************************!*\
  !*** ./src/components/LaunchingStateServer.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LaunchingStateServer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(rsc)/./node_modules/next/dist/api/app-dynamic.js\");\n\n\n\n// Import with dynamic to handle possible import failures\nconst LaunchingStateInitializer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_rsc_src_components_LaunchingStateInitializer_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ./LaunchingStateInitializer */ \"(rsc)/./src/components/LaunchingStateInitializer.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\LaunchingStateServer.tsx -> \" + \"./LaunchingStateInitializer\"\n        ]\n    },\n    ssr: true,\n    loading: ()=>null\n});\n/**\r\n * Server component to get the launching state from environment variables\r\n * This component will always read the most current value from the server environment\r\n */ function LaunchingStateServer() {\n    // Read the environment variable from the server side\n    // Convert string 'true'/'false' to boolean\n    const launchingState = process.env.NEXT_PUBLIC_LAUNCHING_SOON === \"true\";\n    // Pass the value to the client component with error handling\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LaunchingStateInitializer, {\n        launchingState: launchingState\n    }, void 0, false, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\components\\\\LaunchingStateServer.tsx\",\n        lineNumber: 20,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9MYXVuY2hpbmdTdGF0ZVNlcnZlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEwQjtBQUNTO0FBRW5DLHlEQUF5RDtBQUN6RCxNQUFNRSw0QkFBNEJELHdEQUFPQSxDQUN2QyxJQUFNLHlPQUFPOzs7Ozs7SUFDWEUsS0FBSztJQUFNQyxTQUFTLElBQU07O0FBRzlCOzs7Q0FHQyxHQUNjLFNBQVNDO0lBQ3RCLHFEQUFxRDtJQUNyRCwyQ0FBMkM7SUFDM0MsTUFBTUMsaUJBQWlCQyxRQUFRQyxHQUFHLENBQUNDLDBCQUEwQixLQUFLO0lBRWxFLDZEQUE2RDtJQUM3RCxxQkFBTyw4REFBQ1A7UUFBMEJJLGdCQUFnQkE7Ozs7OztBQUNwRCIsInNvdXJjZXMiOlsid2VicGFjazovL2Fua2tvci8uL3NyYy9jb21wb25lbnRzL0xhdW5jaGluZ1N0YXRlU2VydmVyLnRzeD9mOTAxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG4vLyBJbXBvcnQgd2l0aCBkeW5hbWljIHRvIGhhbmRsZSBwb3NzaWJsZSBpbXBvcnQgZmFpbHVyZXNcclxuY29uc3QgTGF1bmNoaW5nU3RhdGVJbml0aWFsaXplciA9IGR5bmFtaWMoXHJcbiAgKCkgPT4gaW1wb3J0KCcuL0xhdW5jaGluZ1N0YXRlSW5pdGlhbGl6ZXInKSxcclxuICB7IHNzcjogdHJ1ZSwgbG9hZGluZzogKCkgPT4gbnVsbCB9XHJcbik7XHJcblxyXG4vKipcclxuICogU2VydmVyIGNvbXBvbmVudCB0byBnZXQgdGhlIGxhdW5jaGluZyBzdGF0ZSBmcm9tIGVudmlyb25tZW50IHZhcmlhYmxlc1xyXG4gKiBUaGlzIGNvbXBvbmVudCB3aWxsIGFsd2F5cyByZWFkIHRoZSBtb3N0IGN1cnJlbnQgdmFsdWUgZnJvbSB0aGUgc2VydmVyIGVudmlyb25tZW50XHJcbiAqL1xyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMYXVuY2hpbmdTdGF0ZVNlcnZlcigpIHtcclxuICAvLyBSZWFkIHRoZSBlbnZpcm9ubWVudCB2YXJpYWJsZSBmcm9tIHRoZSBzZXJ2ZXIgc2lkZVxyXG4gIC8vIENvbnZlcnQgc3RyaW5nICd0cnVlJy8nZmFsc2UnIHRvIGJvb2xlYW5cclxuICBjb25zdCBsYXVuY2hpbmdTdGF0ZSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0xBVU5DSElOR19TT09OID09PSAndHJ1ZSc7XHJcbiAgXHJcbiAgLy8gUGFzcyB0aGUgdmFsdWUgdG8gdGhlIGNsaWVudCBjb21wb25lbnQgd2l0aCBlcnJvciBoYW5kbGluZ1xyXG4gIHJldHVybiA8TGF1bmNoaW5nU3RhdGVJbml0aWFsaXplciBsYXVuY2hpbmdTdGF0ZT17bGF1bmNoaW5nU3RhdGV9IC8+O1xyXG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsImR5bmFtaWMiLCJMYXVuY2hpbmdTdGF0ZUluaXRpYWxpemVyIiwic3NyIiwibG9hZGluZyIsIkxhdW5jaGluZ1N0YXRlU2VydmVyIiwibGF1bmNoaW5nU3RhdGUiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfTEFVTkNISU5HX1NPT04iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/components/LaunchingStateServer.tsx\n");

/***/ })

};
;