import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { jwtDecode } from 'jwt-decode';
import { get, set, isRedisAvailable, CACHE_TTL } from '@/lib/redis';

interface JwtPayload {
  exp: number;
  iat: number;
  data: {
    user: {
      id: string;
      email: string;
    };
  };
}

interface WishlistItem {
  id: string;
  name: string;
  price: string;
  image: string;
  handle: string;
  material: string;
  variantId: string;
}

// GET - Fetch user's wishlist
export async function GET(request: NextRequest) {
  try {
    // Get auth token from cookies
    const cookieStore = cookies();
    const authCookie = cookieStore.get('woo_auth_token');
    
    if (!authCookie || !authCookie.value) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify and decode token
    let userId: string;
    try {
      const decoded = jwtDecode<JwtPayload>(authCookie.value);
      const currentTime = Date.now() / 1000;
      
      if (decoded.exp < currentTime) {
        return NextResponse.json(
          { success: false, message: 'Token expired' },
          { status: 401 }
        );
      }
      
      userId = decoded.data.user.id;
    } catch (error) {
      return NextResponse.json(
        { success: false, message: 'Invalid token' },
        { status: 401 }
      );
    }

    // For now, we'll use localStorage simulation via cookies
    // In a real implementation, this would fetch from a database
    const wishlistCookie = cookieStore.get(`wishlist_${userId}`);
    const wishlist = wishlistCookie ? JSON.parse(wishlistCookie.value) : [];

    return NextResponse.json({
      success: true,
      wishlist
    });

  } catch (error) {
    console.error('Error fetching wishlist:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Save user's wishlist
export async function POST(request: NextRequest) {
  try {
    // Get auth token from cookies
    const cookieStore = cookies();
    const authCookie = cookieStore.get('woo_auth_token');
    
    if (!authCookie || !authCookie.value) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify and decode token
    let userId: string;
    try {
      const decoded = jwtDecode<JwtPayload>(authCookie.value);
      const currentTime = Date.now() / 1000;
      
      if (decoded.exp < currentTime) {
        return NextResponse.json(
          { success: false, message: 'Token expired' },
          { status: 401 }
        );
      }
      
      userId = decoded.data.user.id;
    } catch (error) {
      return NextResponse.json(
        { success: false, message: 'Invalid token' },
        { status: 401 }
      );
    }

    // Get wishlist data from request body
    const { wishlist } = await request.json();
    
    if (!Array.isArray(wishlist)) {
      return NextResponse.json(
        { success: false, message: 'Invalid wishlist data' },
        { status: 400 }
      );
    }

    // For now, we'll store in cookies (in production, use a database)
    // Note: This is a temporary solution - cookies have size limits
    const response = NextResponse.json({
      success: true,
      message: 'Wishlist saved successfully'
    });

    // Set wishlist cookie (expires in 30 days)
    response.cookies.set(`wishlist_${userId}`, JSON.stringify(wishlist), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60 // 30 days
    });

    return response;

  } catch (error) {
    console.error('Error saving wishlist:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
