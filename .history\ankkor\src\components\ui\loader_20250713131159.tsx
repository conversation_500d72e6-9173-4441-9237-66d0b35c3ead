import React from 'react';

interface LoaderProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

const Loader = ({ size = 'md', color = '#2c2c27', className = '' }: LoaderProps) => {
  // Size mappings
  const sizeMap = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div
        className={`${sizeMap[size]} border-2 border-gray-300 border-t-current rounded-full animate-spin`}
        style={{ borderTopColor: color }}
      />
    </div>
  );
};

export default Loader;