import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Heart, ShoppingBag } from 'lucide-react';
import { useCartStore } from '@/lib/wooStore';
import { useWishlistStore } from '@/lib/store';
import { useCustomer } from '@/components/providers/CustomerProvider';
import ImageLoader from '@/components/ui/ImageLoader';
import { DEFAULT_CURRENCY_SYMBOL, DEFAULT_CURRENCY_CODE } from '@/lib/currency';
import { toast } from 'react-hot-toast';

// Helper function to clean price for storage
const cleanPriceForStorage = (price: string | number): string => {
  if (typeof price === 'number') return price.toString();
  if (!price) return '0';

  // Remove currency symbols, commas, and other non-numeric characters except decimal point
  const cleanPrice = price.toString().replace(/[^\d.-]/g, '');
  const parsed = parseFloat(cleanPrice);
  return isNaN(parsed) ? '0' : parsed.toString();
};

interface ProductCardProps {
  id: string;
  name: string;
  price: string;
  image: string;
  slug: string;
  material?: string;
  isNew?: boolean;
  stockStatus?: string;
  compareAtPrice?: string | null;
  regularPrice?: string | null;
  salePrice?: string | null;
  onSale?: boolean;
  currencySymbol?: string;
  currencyCode?: string;
  shortDescription?: string;
  type?: string;
}

const ProductCard = ({
  id,
  name,
  price,
  image,
  slug,
  material,
  isNew = false,
  stockStatus = 'IN_STOCK',
  compareAtPrice = null,
  regularPrice = null,
  salePrice = null,
  onSale = false,
  currencySymbol = DEFAULT_CURRENCY_SYMBOL,
  currencyCode = DEFAULT_CURRENCY_CODE,
  shortDescription,
  type
}: ProductCardProps) => {
  const cart = useCartStore();
  const { addToWishlist, isInWishlist, removeFromWishlist } = useWishlistStore();
  const { isAuthenticated } = useCustomer();

  const inWishlist = isInWishlist(id);
  
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    // Validate product ID before adding to cart
    if (!id || id === '') {
      console.error('Cannot add to cart: Missing product ID for product', name);
      return;
    }
    
    console.log(`Adding product to cart: ${name} (ID: ${id})`);
    
    try {
      await cart.addToCart({
        productId: id,
        quantity: 1,
        name: name,
        price: price,
        image: {
          url: image,
          altText: name
        }
      });
    } catch (error) {
      console.error(`Failed to add ${name} to cart:`, error);
    }
  };
  
  const handleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (inWishlist) {
      removeFromWishlist(id);
      toast.success('Removed from wishlist');
    } else {
      addToWishlist({
        id,
        name,
        price: cleanPriceForStorage(price),
        image,
        handle: slug,
        material: material || 'Material not specified',
        variantId: id // Using product ID as variant ID for WooCommerce
      });

      // Show appropriate success message based on authentication status
      if (isAuthenticated) {
        toast.success('Added to your wishlist');
      } else {
        toast.success('Added to wishlist (saved locally)');
      }
    }
  };

  // Calculate discount percentage if compareAtPrice exists
  const discountPercentage = compareAtPrice && parseFloat(compareAtPrice) > parseFloat(price) 
    ? Math.round(((parseFloat(compareAtPrice) - parseFloat(price)) / parseFloat(compareAtPrice)) * 100) 
    : null;
  
  const isOutOfStock = stockStatus !== 'IN_STOCK';
  
  return (
    <motion.div 
      className="group relative"
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      <Link href={`/product/${slug}`} className="block">
        <div className="relative overflow-hidden mb-4">
          {/* Product Image */}
          <div className="aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden">
            <ImageLoader
              src={image}
              alt={name}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              animate={true}
              className="h-full"
            />
          </div>
          
          {/* Quick Actions */}
          <div className="absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <motion.button
              onClick={handleWishlist}
              className={`p-2 rounded-none ${inWishlist ? 'bg-[#2c2c27]' : 'bg-[#f8f8f5]'}`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label={inWishlist ? "Remove from wishlist" : "Add to wishlist"}
            >
              <Heart className={`h-5 w-5 ${inWishlist ? 'text-[#f4f3f0] fill-current' : 'text-[#2c2c27]'}`} />
            </motion.button>
            
            <motion.button
              onClick={handleAddToCart}
              className={`p-2 rounded-none ${isOutOfStock ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#2c2c27]'} text-[#f4f3f0]`}
              whileHover={isOutOfStock ? {} : { scale: 1.05 }}
              whileTap={isOutOfStock ? {} : { scale: 0.95 }}
              aria-label={isOutOfStock ? "Out of stock" : "Add to cart"}
              disabled={isOutOfStock}
            >
              <ShoppingBag className="h-5 w-5" />
            </motion.button>
          </div>
          
          {/* New Badge */}
          {isNew && (
            <div className="absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider">
              New
            </div>
          )}

          {/* Out of Stock Badge */}
          {isOutOfStock && (
            <div className="absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider">
              Out of Stock
            </div>
          )}

          {/* Discount Badge */}
          {!isOutOfStock && discountPercentage && (
            <div className="absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider">
              {discountPercentage}% Off
            </div>
          )}
        </div>
        
        {/* Product Info */}
        <div className="space-y-2">
          <h3 className="font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2">{name}</h3>

          {material && (
            <p className="text-[#8a8778] text-xs">{material}</p>
          )}

          {/* Product Type */}
          {type && (
            <p className="text-[#8a8778] text-xs capitalize">{type.toLowerCase().replace('_', ' ')}</p>
          )}

          {/* Short Description */}
          {shortDescription && (
            <p className="text-[#5c5c52] text-xs line-clamp-2"
               dangerouslySetInnerHTML={{ __html: shortDescription.replace(/<[^>]*>/g, '') }} />
          )}

          {/* Price Section */}
          <div className="space-y-1">
            <div className="flex items-center gap-2 product-card-price">
              {/* Sale Price or Regular Price */}
              <p className="text-[#2c2c27] font-medium">
                {onSale && salePrice ? (
                  salePrice.toString().includes('₹') || salePrice.toString().includes('$') || salePrice.toString().includes('€') || salePrice.toString().includes('£')
                    ? salePrice
                    : `${currencySymbol}${salePrice}`
                ) : (
                  price.toString().includes('₹') || price.toString().includes('$') || price.toString().includes('€') || price.toString().includes('£')
                    ? price
                    : `${currencySymbol}${price}`
                )}
              </p>

              {/* Regular Price (crossed out when on sale) */}
              {onSale && regularPrice && (
                <p className="text-[#8a8778] text-xs line-through product-card-compare-price">
                  {regularPrice.toString().includes('₹') || regularPrice.toString().includes('$') || regularPrice.toString().includes('€') || regularPrice.toString().includes('£')
                    ? regularPrice
                    : `${currencySymbol}${regularPrice}`}
                </p>
              )}

              {/* Compare At Price (fallback) */}
              {!onSale && compareAtPrice && parseFloat(compareAtPrice.toString().replace(/[₹$€£]/g, '')) > parseFloat(price.toString().replace(/[₹$€£]/g, '')) && (
                <p className="text-[#8a8778] text-xs line-through product-card-compare-price">
                  {compareAtPrice.toString().includes('₹') || compareAtPrice.toString().includes('$') || compareAtPrice.toString().includes('€') || compareAtPrice.toString().includes('£')
                    ? compareAtPrice
                    : `${currencySymbol}${compareAtPrice}`}
                </p>
              )}
            </div>

            {/* Stock Status */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {stockStatus === 'IN_STOCK' ? (
                  <span className="text-green-600 text-xs font-medium">✓ In Stock</span>
                ) : stockStatus === 'OUT_OF_STOCK' ? (
                  <span className="text-red-600 text-xs font-medium">✗ Out of Stock</span>
                ) : stockStatus === 'ON_BACKORDER' ? (
                  <span className="text-orange-600 text-xs font-medium">⏳ Backorder</span>
                ) : (
                  <span className="text-gray-600 text-xs font-medium">? Unknown</span>
                )}
              </div>

              {/* Sale Badge */}
              {onSale && (
                <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                  Sale
                </span>
              )}
            </div>
          </div>
        </div>
      </Link>
    </motion.div>
  );
};

export default ProductCard; 