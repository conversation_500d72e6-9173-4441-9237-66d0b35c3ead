# Authentication Debug Guide

## Issue Fixed
The profile update was failing because of a mismatch between how authentication tokens were stored and retrieved:

1. **Login/Registration**: API routes set HTTP-only cookies (secure, can't be accessed by client JS)
2. **Profile Update**: `auth.ts` was trying to read regular cookies (accessible by client JS)

## Solution Implemented
- Modified `updateCustomerProfile()` in `auth.ts` to use a new API endpoint
- Created `/api/auth/update-profile` endpoint that has access to HTTP-only cookies
- This maintains security while allowing profile updates

## Testing Steps

### 1. Check Authentication Status
Open browser developer tools and check:
- **Application/Storage tab**: Look for `woo_auth_token` cookie
- **Console**: Check for any authentication errors

### 2. Test Profile Update
1. Make sure you're logged in to the account
2. Go to account profile page
3. Try updating first name, last name, or phone
4. Check browser console for any errors

### 3. Debug Authentication Flow
If still having issues, check these in browser console:

```javascript
// Check if auth cookie exists (this will return undefined for HTTP-only cookies)
document.cookie.includes('woo_auth_token')

// Test the auth API endpoint
fetch('/api/auth/me', { credentials: 'include' })
  .then(r => r.json())
  .then(console.log)

// Test the update profile endpoint
fetch('/api/auth/update-profile', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({ firstName: 'Test' })
}).then(r => r.json()).then(console.log)
```

## Expected Behavior
- Profile update should work without "Not authenticated" errors
- Success message should appear after updating profile
- Customer data should be refreshed in the UI

## If Still Having Issues
1. Clear browser cookies and localStorage
2. Log out and log back in
3. Check server logs for any GraphQL errors
4. Verify WooCommerce GraphQL endpoint is working
