import { GraphQLClient } from 'graphql-request';

// Create a GraphQL client instance for WooCommerce
const endpoint = process.env.NEXT_PUBLIC_WORDPRESS_URL 
  ? `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/graphql` 
  : '';

// Client for server-side requests
export const getWooGraphQLClient = () => {
  return new GraphQLClient(endpoint, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Client for authenticated requests (with JWT)
export const getAuthenticatedWooGraphQLClient = (token: string) => {
  return new GraphQLClient(endpoint, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  });
};

// Function to execute GraphQL queries/mutations
export const executeGraphQL = async <T = any, V = any>(
  query: string, 
  variables?: V, 
  token?: string
): Promise<T> => {
  try {
    const client = token 
      ? getAuthenticatedWooGraphQLClient(token) 
      : getWooGraphQLClient();
    
    return await client.request<T>(query, variables);
  } catch (error) {
    console.error('GraphQL request error:', error);
    throw error;
  }
}; 