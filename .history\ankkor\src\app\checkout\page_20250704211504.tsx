'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { useCartStore } from '@/lib/wooStore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2 } from 'lucide-react';

interface CheckoutFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address1: string;
  address2: string;
  city: string;
  state: string;
  postcode: string;
  country: string;
  sameAsBilling: boolean;
  shippingFirstName: string;
  shippingLastName: string;
  shippingAddress1: string;
  shippingAddress2: string;
  shippingCity: string;
  shippingState: string;
  shippingPostcode: string;
  shippingCountry: string;
  paymentMethod: string;
  createAccount: boolean;
  password: string;
  notes: string;
}

export default function CheckoutPage() {
  const router = useRouter();
  const cartStore = useCartStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableCountries, setAvailableCountries] = useState([
    { code: 'US', name: 'United States' },
    { code: 'CA', name: 'Canada' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'AU', name: 'Australia' }
  ]);
  
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<CheckoutFormData>({
    defaultValues: {
      country: 'US',
      shippingCountry: 'US',
      sameAsBilling: true,
      paymentMethod: 'stripe'
    }
  });
  
  // Watch for changes to the "same as billing" checkbox
  const sameAsBilling = watch('sameAsBilling');
  
  // Update shipping fields when "same as billing" changes
  useEffect(() => {
    if (sameAsBilling) {
      const billingFields = [
        { billing: 'firstName', shipping: 'shippingFirstName' },
        { billing: 'lastName', shipping: 'shippingLastName' },
        { billing: 'address1', shipping: 'shippingAddress1' },
        { billing: 'address2', shipping: 'shippingAddress2' },
        { billing: 'city', shipping: 'shippingCity' },
        { billing: 'state', shipping: 'shippingState' },
        { billing: 'postcode', shipping: 'shippingPostcode' },
        { billing: 'country', shipping: 'shippingCountry' }
      ];
      
      billingFields.forEach(field => {
        const billingValue = watch(field.billing as keyof CheckoutFormData);
        setValue(field.shipping as keyof CheckoutFormData, billingValue);
      });
    }
  }, [sameAsBilling, setValue, watch]);
  
  // Check if cart is empty and redirect if needed
  useEffect(() => {
    if (cartStore.items.length === 0) {
      router.push('/');
    }
  }, [cartStore.items, router]);
  
  const onSubmit = async (data: CheckoutFormData) => {
    setIsSubmitting(true);
    setError(null);
    
    try {
      // Create order payload
      const orderData = {
        billing: {
          first_name: data.firstName,
          last_name: data.lastName,
          email: data.email,
          phone: data.phone,
          address_1: data.address1,
          address_2: data.address2,
          city: data.city,
          state: data.state,
          postcode: data.postcode,
          country: data.country
        },
        shipping: {
          first_name: data.sameAsBilling ? data.firstName : data.shippingFirstName,
          last_name: data.sameAsBilling ? data.lastName : data.shippingLastName,
          address_1: data.sameAsBilling ? data.address1 : data.shippingAddress1,
          address_2: data.sameAsBilling ? data.address2 : data.shippingAddress2,
          city: data.sameAsBilling ? data.city : data.shippingCity,
          state: data.sameAsBilling ? data.state : data.shippingState,
          postcode: data.sameAsBilling ? data.postcode : data.shippingPostcode,
          country: data.sameAsBilling ? data.country : data.shippingCountry
        },
        payment_method: data.paymentMethod,
        line_items: cartStore.items.map(item => ({
          product_id: parseInt(item.productId),
          variation_id: item.variationId ? parseInt(item.variationId) : undefined,
          quantity: item.quantity
        })),
        customer_note: data.notes
      };
      
      // Send order to API
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(orderData)
      });
      
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || 'Failed to create order');
      }
      
      // Clear cart and redirect to success page
      cartStore.clearCart();
      router.push(`/checkout/success?order_id=${result.orderId}`);
      
    } catch (err) {
      console.error('Checkout error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during checkout');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Calculate order summary
  const subtotal = cartStore.subtotal();
  const shipping = 0; // This would normally be calculated based on shipping method
  const tax = 0; // This would normally be calculated based on location
  const total = subtotal + shipping + tax;
  
  if (cartStore.items.length === 0) {
    return null; // Will redirect in useEffect
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-serif mb-8">Checkout</h1>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Checkout Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Billing Information */}
            <div className="bg-white p-6 border rounded-lg shadow-sm">
              <h2 className="text-xl font-medium mb-4">Billing Information</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    {...register('firstName', { required: 'First name is required' })}
                    className={errors.firstName ? 'border-red-300' : ''}
                  />
                  {errors.firstName && (
                    <p className="text-sm text-red-500 mt-1">{errors.firstName.message}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    {...register('lastName', { required: 'Last name is required' })}
                    className={errors.lastName ? 'border-red-300' : ''}
                  />
                  {errors.lastName && (
                    <p className="text-sm text-red-500 mt-1">{errors.lastName.message}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email', { 
                      required: 'Email is required',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Invalid email address'
                      }
                    })}
                    className={errors.email ? 'border-red-300' : ''}
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    {...register('phone', { required: 'Phone number is required' })}
                    className={errors.phone ? 'border-red-300' : ''}
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
                  )}
                </div>
                
                <div className="md:col-span-2">
                  <Label htmlFor="address1">Address Line 1</Label>
                  <Input
                    id="address1"
                    {...register('address1', { required: 'Address is required' })}
                    className={errors.address1 ? 'border-red-300' : ''}
                  />
                  {errors.address1 && (
                    <p className="text-sm text-red-500 mt-1">{errors.address1.message}</p>
                  )}
                </div>
                
                <div className="md:col-span-2">
                  <Label htmlFor="address2">Address Line 2 (Optional)</Label>
                  <Input id="address2" {...register('address2')} />
                </div>
                
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    {...register('city', { required: 'City is required' })}
                    className={errors.city ? 'border-red-300' : ''}
                  />
                  {errors.city && (
                    <p className="text-sm text-red-500 mt-1">{errors.city.message}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="state">State/Province</Label>
                  <Input
                    id="state"
                    {...register('state', { required: 'State is required' })}
                    className={errors.state ? 'border-red-300' : ''}
                  />
                  {errors.state && (
                    <p className="text-sm text-red-500 mt-1">{errors.state.message}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="postcode">ZIP/Postal Code</Label>
                  <Input
                    id="postcode"
                    {...register('postcode', { required: 'Postal code is required' })}
                    className={errors.postcode ? 'border-red-300' : ''}
                  />
                  {errors.postcode && (
                    <p className="text-sm text-red-500 mt-1">{errors.postcode.message}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="country">Country</Label>
                  <select
                    id="country"
                    {...register('country', { required: 'Country is required' })}
                    className="w-full border border-gray-300 rounded-md p-2"
                  >
                    {availableCountries.map(country => (
                      <option key={country.code} value={country.code}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
            
            {/* Shipping Information */}
            <div className="bg-white p-6 border rounded-lg shadow-sm">
              <div className="flex items-center mb-4">
                <h2 className="text-xl font-medium">Shipping Information</h2>
                <div className="ml-4 flex items-center">
                  <Checkbox
                    id="sameAsBilling"
                    checked={sameAsBilling}
                    onCheckedChange={(checked) => setValue('sameAsBilling', checked === true)}
                  />
                  <label htmlFor="sameAsBilling" className="ml-2 text-sm">
                    Same as billing address
                  </label>
                </div>
              </div>
              
              {!sameAsBilling && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="shippingFirstName">First Name</Label>
                    <Input
                      id="shippingFirstName"
                      {...register('shippingFirstName', { 
                        required: !sameAsBilling ? 'First name is required' : false 
                      })}
                      className={errors.shippingFirstName ? 'border-red-300' : ''}
                    />
                    {errors.shippingFirstName && (
                      <p className="text-sm text-red-500 mt-1">{errors.shippingFirstName.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="shippingLastName">Last Name</Label>
                    <Input
                      id="shippingLastName"
                      {...register('shippingLastName', { 
                        required: !sameAsBilling ? 'Last name is required' : false 
                      })}
                      className={errors.shippingLastName ? 'border-red-300' : ''}
                    />
                    {errors.shippingLastName && (
                      <p className="text-sm text-red-500 mt-1">{errors.shippingLastName.message}</p>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <Label htmlFor="shippingAddress1">Address Line 1</Label>
                    <Input
                      id="shippingAddress1"
                      {...register('shippingAddress1', { 
                        required: !sameAsBilling ? 'Address is required' : false 
                      })}
                      className={errors.shippingAddress1 ? 'border-red-300' : ''}
                    />
                    {errors.shippingAddress1 && (
                      <p className="text-sm text-red-500 mt-1">{errors.shippingAddress1.message}</p>
                    )}
                  </div>
                  
                  <div className="md:col-span-2">
                    <Label htmlFor="shippingAddress2">Address Line 2 (Optional)</Label>
                    <Input id="shippingAddress2" {...register('shippingAddress2')} />
                  </div>
                  
                  <div>
                    <Label htmlFor="shippingCity">City</Label>
                    <Input
                      id="shippingCity"
                      {...register('shippingCity', { 
                        required: !sameAsBilling ? 'City is required' : false 
                      })}
                      className={errors.shippingCity ? 'border-red-300' : ''}
                    />
                    {errors.shippingCity && (
                      <p className="text-sm text-red-500 mt-1">{errors.shippingCity.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="shippingState">State/Province</Label>
                    <Input
                      id="shippingState"
                      {...register('shippingState', { 
                        required: !sameAsBilling ? 'State is required' : false 
                      })}
                      className={errors.shippingState ? 'border-red-300' : ''}
                    />
                    {errors.shippingState && (
                      <p className="text-sm text-red-500 mt-1">{errors.shippingState.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="shippingPostcode">ZIP/Postal Code</Label>
                    <Input
                      id="shippingPostcode"
                      {...register('shippingPostcode', { 
                        required: !sameAsBilling ? 'Postal code is required' : false 
                      })}
                      className={errors.shippingPostcode ? 'border-red-300' : ''}
                    />
                    {errors.shippingPostcode && (
                      <p className="text-sm text-red-500 mt-1">{errors.shippingPostcode.message}</p>
                    )}
                  </div>
                  
                  <div>
                    <Label htmlFor="shippingCountry">Country</Label>
                    <select
                      id="shippingCountry"
                      {...register('shippingCountry', { 
                        required: !sameAsBilling ? 'Country is required' : false 
                      })}
                      className="w-full border border-gray-300 rounded-md p-2"
                    >
                      {availableCountries.map(country => (
                        <option key={country.code} value={country.code}>
                          {country.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              )}
            </div>
            
            {/* Payment Method */}
            <div className="bg-white p-6 border rounded-lg shadow-sm">
              <h2 className="text-xl font-medium mb-4">Payment Method</h2>
              
              <div className="space-y-3">
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="stripe"
                    value="stripe"
                    {...register('paymentMethod', { required: true })}
                    className="mr-2"
                  />
                  <label htmlFor="stripe">Credit Card (Stripe)</label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="paypal"
                    value="paypal"
                    {...register('paymentMethod', { required: true })}
                    className="mr-2"
                  />
                  <label htmlFor="paypal">PayPal</label>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="cod"
                    value="cod"
                    {...register('paymentMethod', { required: true })}
                    className="mr-2"
                  />
                  <label htmlFor="cod">Cash on Delivery</label>
                </div>
              </div>
            </div>
            
            {/* Additional Information */}
            <div className="bg-white p-6 border rounded-lg shadow-sm">
              <h2 className="text-xl font-medium mb-4">Additional Information</h2>
              
              <div>
                <Label htmlFor="notes">Order Notes (Optional)</Label>
                <textarea
                  id="notes"
                  {...register('notes')}
                  rows={4}
                  className="w-full border border-gray-300 rounded-md p-2"
                  placeholder="Notes about your order, e.g. special delivery instructions"
                />
              </div>
              
              <div className="mt-4 flex items-center">
                <Checkbox
                  id="createAccount"
                  checked={watch('createAccount')}
                  onCheckedChange={(checked) => setValue('createAccount', checked === true)}
                />
                <label htmlFor="createAccount" className="ml-2 text-sm">
                  Create an account?
                </label>
              </div>
              
              {watch('createAccount') && (
                <div className="mt-4">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    {...register('password', { 
                      required: watch('createAccount') ? 'Password is required' : false,
                      minLength: {
                        value: 6,
                        message: 'Password must be at least 6 characters'
                      }
                    })}
                    className={errors.password ? 'border-red-300' : ''}
                  />
                  {errors.password && (
                    <p className="text-sm text-red-500 mt-1">{errors.password.message}</p>
                  )}
                </div>
              )}
            </div>
            
            <Button
              type="submit"
              className="w-full py-6 bg-[#2c2c27] hover:bg-[#3c3c37] text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                `Place Order - ₹${total.toFixed(2)}`
              )}
            </Button>
          </form>
        </div>
        
        {/* Order Summary */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 border rounded-lg shadow-sm sticky top-8">
            <h2 className="text-xl font-medium mb-4">Order Summary</h2>
            
            <div className="space-y-4">
              {cartStore.items.map(item => (
                <div key={item.id} className="flex gap-4 py-2 border-b">
                  {item.image?.url && (
                    <div className="relative h-16 w-16 bg-gray-100 flex-shrink-0">
                      <img
                        src={item.image.url}
                        alt={item.name}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  )}
                  <div className="flex-1">
                    <h3 className="text-sm font-medium">{item.name}</h3>
                    <p className="text-sm text-gray-600">
                      ${parseFloat(item.price).toFixed(2)} × {item.quantity}
                    </p>
                  </div>
                  <div className="text-right">
                    ${(parseFloat(item.price) * item.quantity).toFixed(2)}
                  </div>
                </div>
              ))}
              
              <div className="pt-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span>{shipping === 0 ? 'Free' : `$${shipping.toFixed(2)}`}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span>${tax.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-medium pt-2 border-t">
                  <span>Total</span>
                  <span>${total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 