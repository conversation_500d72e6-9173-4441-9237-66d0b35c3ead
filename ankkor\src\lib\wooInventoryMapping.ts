import { Redis } from '@upstash/redis';
import { kv } from '@vercel/kv';

// Type for inventory mapping
type InventoryMap = Record<string, string>;

// Redis key prefix for inventory mappings
const KEY_PREFIX = 'woo:inventory:mapping:';
// Redis key for the mapping between Shopify and WooCommerce IDs
const SHOPIFY_TO_WOO_KEY = 'shopify:to:woo:mapping';

// Initialize Redis client with support for both Upstash Redis and Vercel KV variables
const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL || 
       process.env.NEXT_PUBLIC_KV_REST_API_URL || 
       '',
  token: process.env.UPSTASH_REDIS_REST_TOKEN || 
         process.env.NEXT_PUBLIC_KV_REST_API_TOKEN || 
         '',
});

// In-memory fallback for local development or when Redis is unavailable
const memoryStorage: InventoryMap = {};
const shopifyToWooMemoryStorage: Record<string, string> = {};

// Inventory mapping type
export interface InventoryMapping {
  [productId: string]: {
    wooId: string;
    inventory: number;
    sku: string;
    title: string;
    lastUpdated: string;
  };
}

// Key for storing inventory mapping in KV store
const INVENTORY_MAPPING_KEY = 'woo-inventory-mapping';

/**
 * Check if Redis is available
 */
function isRedisAvailable(): boolean {
  return Boolean(
    (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) ||
    (process.env.NEXT_PUBLIC_KV_REST_API_URL && process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)
  );
}

/**
 * Load inventory mapping from storage
 * Maps WooCommerce product IDs to product slugs
 * 
 * @returns A record mapping product IDs to product slugs
 */
export async function loadInventoryMap(): Promise<InventoryMap> {
  // Use Redis if available
  if (isRedisAvailable()) {
    try {
      // Get all keys with our prefix
      const keys = await redis.keys(`${KEY_PREFIX}*`);
      
      if (keys.length === 0) {
        console.log('No existing WooCommerce inventory mappings found in Redis');
        return {};
      }
      
      // Create a mapping object
      const map: InventoryMap = {};
      
      // Get all values in a single batch operation
      const values = await redis.mget(...keys);
      
      // Populate the mapping object
      keys.forEach((key, index) => {
        const productId = key.replace(KEY_PREFIX, '');
        const productSlug = values[index] as string;
        map[productId] = productSlug;
      });
      
      console.log(`Loaded WooCommerce inventory mapping with ${Object.keys(map).length} entries from Redis`);
      return map;
    } catch (error) {
      console.error('Error loading WooCommerce inventory mapping from Redis:', error);
      console.log('Falling back to in-memory storage');
      return { ...memoryStorage };
    }
  } else {
    // Fallback to in-memory when Redis is not available
    return { ...memoryStorage };
  }
}

/**
 * Save inventory mapping to storage
 * 
 * @param map The inventory mapping to save
 */
export async function saveInventoryMap(map: InventoryMap): Promise<void> {
  // Use Redis if available
  if (isRedisAvailable()) {
    try {
      // Convert map to array of Redis commands
      const pipeline = redis.pipeline();
      
      // First clear existing keys with this prefix
      const existingKeys = await redis.keys(`${KEY_PREFIX}*`);
      if (existingKeys.length > 0) {
        pipeline.del(...existingKeys);
      }
      
      // Set new key-value pairs
      Object.entries(map).forEach(([productId, productSlug]) => {
        pipeline.set(`${KEY_PREFIX}${productId}`, productSlug);
      });
      
      // Execute all commands in a single transaction
      await pipeline.exec();
      
      console.log(`Saved WooCommerce inventory mapping with ${Object.keys(map).length} entries to Redis`);
    } catch (error) {
      console.error('Error saving WooCommerce inventory mapping to Redis:', error);
      console.log('Falling back to in-memory storage');
      
      // Update in-memory storage as fallback
      Object.assign(memoryStorage, map);
    }
  } else {
    // Fallback to in-memory when Redis is not available
    Object.assign(memoryStorage, map);
    console.log(`Saved WooCommerce inventory mapping with ${Object.keys(map).length} entries to memory`);
  }
}

/**
 * Add a mapping between a WooCommerce product ID and a product slug
 * 
 * @param productId The WooCommerce product ID
 * @param productSlug The product slug
 * @returns True if the mapping was added or updated, false if there was an error
 */
export async function addInventoryMapping(productId: string, productSlug: string): Promise<boolean> {
  try {
    if (isRedisAvailable()) {
      await redis.set(`${KEY_PREFIX}${productId}`, productSlug);
      console.log(`Added WooCommerce mapping to Redis: ${productId} -> ${productSlug}`);
    } else {
      memoryStorage[productId] = productSlug;
      console.log(`Added WooCommerce mapping to memory: ${productId} -> ${productSlug}`);
    }
    return true;
  } catch (error) {
    console.error('Error adding WooCommerce inventory mapping:', error);
    
    // Try memory as fallback
    try {
      memoryStorage[productId] = productSlug;
      console.log(`Added WooCommerce mapping to memory fallback: ${productId} -> ${productSlug}`);
      return true;
    } catch (memError) {
      console.error('Error adding to memory fallback:', memError);
      return false;
    }
  }
}

/**
 * Get the product slug associated with a WooCommerce product ID
 * 
 * @param productId The WooCommerce product ID
 * @returns The product slug, or null if not found
 */
export async function getProductSlugFromInventory(productId: string): Promise<string | null> {
  try {
    if (isRedisAvailable()) {
      const slug = await redis.get(`${KEY_PREFIX}${productId}`);
      return slug as string || null;
    } else {
      return memoryStorage[productId] || null;
    }
  } catch (error) {
    console.error('Error getting product slug from Redis:', error);
    
    // Try memory as fallback
    try {
      return memoryStorage[productId] || null;
    } catch (memError) {
      console.error('Error getting from memory fallback:', memError);
      return null;
    }
  }
}

/**
 * Batch update multiple WooCommerce inventory mappings
 * 
 * @param mappings An array of product ID to product slug mappings
 * @returns True if all mappings were successfully updated, false otherwise
 */
export async function updateInventoryMappings(
  mappings: Array<{ productId: string; productSlug: string }>
): Promise<boolean> {
  try {
    if (isRedisAvailable()) {
      const pipeline = redis.pipeline();
      
      for (const { productId, productSlug } of mappings) {
        pipeline.set(`${KEY_PREFIX}${productId}`, productSlug);
      }
      
      await pipeline.exec();
      console.log(`Updated ${mappings.length} WooCommerce inventory mappings in Redis`);
    } else {
      for (const { productId, productSlug } of mappings) {
        memoryStorage[productId] = productSlug;
      }
      console.log(`Updated ${mappings.length} WooCommerce inventory mappings in memory`);
    }
    
    return true;
  } catch (error) {
    console.error('Error batch updating WooCommerce inventory mappings:', error);
    return false;
  }
}

/**
 * Get all WooCommerce inventory mappings
 * 
 * @returns The complete inventory map
 */
export async function getAllInventoryMappings(): Promise<InventoryMap> {
  return await loadInventoryMap();
}

/**
 * Clear all WooCommerce inventory mappings
 * 
 * @returns True if successfully cleared, false otherwise
 */
export async function clearInventoryMappings(): Promise<boolean> {
  try {
    if (isRedisAvailable()) {
      const keys = await redis.keys(`${KEY_PREFIX}*`);
      
      if (keys.length > 0) {
        await redis.del(...keys);
      }
      
      console.log('Cleared all WooCommerce inventory mappings from Redis');
    }
    
    // Clear memory storage regardless of Redis availability
    Object.keys(memoryStorage).forEach(key => {
      delete memoryStorage[key];
    });
    
    return true;
  } catch (error) {
    console.error('Error clearing WooCommerce inventory mappings:', error);
    return false;
  }
}

/**
 * Store a mapping from Shopify ID to WooCommerce ID
 * This is used during migration to maintain data lineage
 * 
 * @param shopifyId The original Shopify product ID or inventory item ID
 * @param wooId The corresponding WooCommerce product ID
 * @returns True if successful, false otherwise
 */
export async function mapShopifyToWooId(shopifyId: string, wooId: string): Promise<boolean> {
  try {
    if (isRedisAvailable()) {
      await redis.hset(SHOPIFY_TO_WOO_KEY, shopifyId, wooId);
      console.log(`Mapped Shopify ID ${shopifyId} to WooCommerce ID ${wooId} in Redis`);
    } else {
      shopifyToWooMemoryStorage[shopifyId] = wooId;
      console.log(`Mapped Shopify ID ${shopifyId} to WooCommerce ID ${wooId} in memory`);
    }
    return true;
  } catch (error) {
    console.error(`Error mapping Shopify ID ${shopifyId} to WooCommerce ID:`, error);
    
    // Try memory as fallback
    try {
      shopifyToWooMemoryStorage[shopifyId] = wooId;
      return true;
    } catch (memError) {
      console.error('Error adding to memory fallback:', memError);
      return false;
    }
  }
}

/**
 * Get the WooCommerce ID corresponding to a Shopify ID
 * 
 * @param shopifyId The original Shopify product ID or inventory item ID
 * @returns The corresponding WooCommerce ID, or null if not found
 */
export async function getWooIdFromShopifyId(shopifyId: string): Promise<string | null> {
  try {
    if (isRedisAvailable()) {
      const wooId = await redis.hget(SHOPIFY_TO_WOO_KEY, shopifyId);
      return wooId as string || null;
    } else {
      return shopifyToWooMemoryStorage[shopifyId] || null;
    }
  } catch (error) {
    console.error(`Error getting WooCommerce ID for Shopify ID ${shopifyId}:`, error);
    
    // Try memory as fallback
    try {
      return shopifyToWooMemoryStorage[shopifyId] || null;
    } catch (memError) {
      console.error('Error getting from memory fallback:', memError);
      return null;
    }
  }
}

/**
 * Get all Shopify to WooCommerce ID mappings
 * 
 * @returns Record of Shopify IDs to WooCommerce IDs
 */
export async function getAllShopifyToWooMappings(): Promise<Record<string, string>> {
  try {
    if (isRedisAvailable()) {
      const mappings = await redis.hgetall(SHOPIFY_TO_WOO_KEY);
      return mappings as Record<string, string> || {};
    } else {
      return { ...shopifyToWooMemoryStorage };
    }
  } catch (error) {
    console.error('Error getting all Shopify to WooCommerce mappings:', error);
    return { ...shopifyToWooMemoryStorage };
  }
}

/**
 * Initialize inventory mappings from WooCommerce products
 * This function should be called after initial product import or periodically to refresh the mappings
 * 
 * @param products Array of WooCommerce products with id and slug properties
 * @returns True if successfully initialized, false otherwise
 */
export async function initializeFromProducts(
  products: Array<{ id: string; slug: string; shopifyId?: string }>
): Promise<boolean> {
  try {
    const inventoryMappings: Array<{ productId: string; productSlug: string }> = [];
    const idMappings: Array<{ shopifyId: string; wooId: string }> = [];
    
    for (const product of products) {
      // Add to inventory mappings
      inventoryMappings.push({
        productId: product.id,
        productSlug: product.slug
      });
      
      // If this product has a Shopify ID, add to ID mappings
      if (product.shopifyId) {
        idMappings.push({
          shopifyId: product.shopifyId,
          wooId: product.id
        });
      }
    }
    
    // Update inventory mappings
    await updateInventoryMappings(inventoryMappings);
    
    // Update ID mappings
    for (const { shopifyId, wooId } of idMappings) {
      await mapShopifyToWooId(shopifyId, wooId);
    }
    
    console.log(`Initialized ${inventoryMappings.length} inventory mappings and ${idMappings.length} ID mappings`);
    return true;
  } catch (error) {
    console.error('Error initializing inventory mappings from products:', error);
    return false;
  }
}

/**
 * Get the current inventory mapping
 * @returns Promise with the inventory mapping
 */
export async function getInventoryMapping(): Promise<InventoryMapping> {
  try {
    // Try to get the mapping from KV store
    if (kv) {
      const mapping = await kv.get<InventoryMapping>(INVENTORY_MAPPING_KEY);
      if (mapping) {
        return mapping;
      }
    }
    
    // If KV store is not available or mapping doesn't exist, return empty mapping
    return {};
  } catch (error) {
    console.error('Error getting inventory mapping:', error);
    return {};
  }
}

/**
 * Update the inventory mapping
 * @param mapping - The new inventory mapping
 * @returns Promise indicating success or failure
 */
export async function updateInventoryMapping(mapping: InventoryMapping): Promise<boolean> {
  try {
    // Try to update the mapping in KV store
    if (kv) {
      await kv.set(INVENTORY_MAPPING_KEY, mapping);
      return true;
    }
    
    // If KV store is not available, log a warning
    console.warn('KV store not available, inventory mapping not persisted');
    return false;
  } catch (error) {
    console.error('Error updating inventory mapping:', error);
    return false;
  }
} 