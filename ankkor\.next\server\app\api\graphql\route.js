"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/graphql/route";
exports.ids = ["app/api/graphql/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/graphql/route.ts */ \"(rsc)/./src/app/api/graphql/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/graphql/route\",\n        pathname: \"/api/graphql\",\n        filename: \"route\",\n        bundlePath: \"app/api/graphql/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\graphql\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_graphql_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/graphql/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/graphql/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/graphql/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n/**\r\n * GraphQL Proxy to handle CORS issues when connecting to WooCommerce\r\n * This endpoint forwards GraphQL requests to the WordPress site and handles CORS\r\n */ async function POST(request) {\n    try {\n        // Get the GraphQL query from the request body\n        const body = await request.json();\n        // WordPress/WooCommerce GraphQL endpoint from env variables\n        const graphqlEndpoint = process.env.WOOCOMMERCE_GRAPHQL_URL || \"https://lightpink-eagle-376738.hostingersite.com/graphql\";\n        // Prepare headers for the request to WooCommerce\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            \"Accept\": \"application/json\"\n        };\n        // Forward session token if present in the request\n        const sessionHeader = request.headers.get(\"woocommerce-session\");\n        if (sessionHeader) {\n            headers[\"woocommerce-session\"] = sessionHeader;\n        }\n        // Forward the request to WordPress GraphQL\n        const response = await fetch(graphqlEndpoint, {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(body)\n        });\n        // Get the response data\n        const data = await response.json();\n        // Prepare response headers\n        const responseHeaders = {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session\"\n        };\n        // Forward session token from WooCommerce response if present\n        const responseSessionHeader = response.headers.get(\"woocommerce-session\");\n        if (responseSessionHeader) {\n            responseHeaders[\"woocommerce-session\"] = responseSessionHeader;\n        }\n        // Return the response with CORS headers\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status,\n            headers: responseHeaders\n        });\n    } catch (error) {\n        console.error(\"GraphQL proxy error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            errors: [\n                {\n                    message: error instanceof Error ? error.message : \"Unknown error occurred\"\n                }\n            ]\n        }, {\n            status: 500,\n            headers: {\n                \"Access-Control-Allow-Origin\": \"*\",\n                \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n                \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session\"\n            }\n        });\n    }\n}\n/**\r\n * Handle OPTIONS requests for CORS preflight\r\n */ async function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 204,\n        headers: {\n            \"Access-Control-Allow-Origin\": \"*\",\n            \"Access-Control-Allow-Methods\": \"POST, OPTIONS\",\n            \"Access-Control-Allow-Headers\": \"Content-Type, Authorization, woocommerce-session\",\n            \"Access-Control-Max-Age\": \"86400\"\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/graphql/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgraphql%2Froute&page=%2Fapi%2Fgraphql%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgraphql%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();