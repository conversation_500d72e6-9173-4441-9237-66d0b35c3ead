'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useCartStore } from '@/lib/store';
import { Check, ArrowRight, ShoppingBag } from 'lucide-react';
import { motion } from 'framer-motion';

export default function ThankYouPage() {
  const { clearCart } = useCartStore();
  
  // Clear the cart when the thank you page loads
  useEffect(() => {
    clearCart();
  }, [clearCart]);
  
  return (
    <div className="container mx-auto px-4 py-16 min-h-screen flex flex-col">
      <div className="flex-1 flex flex-col items-center justify-center text-center max-w-2xl mx-auto">
        <motion.div 
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-[#f4f3f0] rounded-full p-6 mb-8"
        >
          <Check className="h-12 w-12 text-[#2c2c27]" />
        </motion.div>
        
        <h1 className="text-4xl font-serif font-bold text-[#2c2c27] mb-6">
          Thank You for Your Order
        </h1>
        
        <p className="text-lg text-[#5c5c52] mb-8 max-w-lg mx-auto">
          Your order has been successfully placed. You should receive a confirmation email shortly.
        </p>
        
        <div className="bg-[#f8f8f5] border border-[#e5e2d9] p-6 rounded-md mb-8 w-full max-w-md mx-auto">
          <h2 className="text-xl font-serif text-[#2c2c27] mb-4">What's Next?</h2>
          
          <div className="space-y-4 text-left">
            <div className="flex">
              <div className="flex-shrink-0 mr-4">
                <div className="bg-[#e5e2d9] h-8 w-8 rounded-full flex items-center justify-center">
                  <span className="text-[#2c2c27] font-medium">1</span>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-[#2c2c27]">Order Confirmation</h3>
                <p className="text-sm text-[#5c5c52]">You'll receive an email confirmation with your order details.</p>
              </div>
            </div>
            
            <div className="flex">
              <div className="flex-shrink-0 mr-4">
                <div className="bg-[#e5e2d9] h-8 w-8 rounded-full flex items-center justify-center">
                  <span className="text-[#2c2c27] font-medium">2</span>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-[#2c2c27]">Order Processing</h3>
                <p className="text-sm text-[#5c5c52]">We'll start preparing your order for shipment.</p>
              </div>
            </div>
            
            <div className="flex">
              <div className="flex-shrink-0 mr-4">
                <div className="bg-[#e5e2d9] h-8 w-8 rounded-full flex items-center justify-center">
                  <span className="text-[#2c2c27] font-medium">3</span>
                </div>
              </div>
              <div>
                <h3 className="font-medium text-[#2c2c27]">Shipping</h3>
                <p className="text-sm text-[#5c5c52]">You'll receive a shipping confirmation with tracking information when your order ships.</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-4 justify-center">
          <Link 
            href="/"
            className="px-6 py-3 bg-[#2c2c27] text-white text-sm uppercase tracking-wider flex items-center hover:bg-[#3d3d35] transition-colors"
          >
            Continue Shopping <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
          
          <Link 
            href="/collection"
            className="px-6 py-3 border border-[#2c2c27] text-[#2c2c27] text-sm uppercase tracking-wider flex items-center hover:bg-[#f4f3f0] transition-colors"
          >
            Browse Collections <ShoppingBag className="ml-2 h-4 w-4" />
          </Link>
        </div>
      </div>
      
      <div className="mt-12 text-center">
        <Image 
          src="/logo.PNG" 
          alt="Ankkor" 
          width={150} 
          height={50} 
          className="mx-auto mb-4"
        />
        <p className="text-sm text-[#8a8778]">Quality craftsmanship for the discerning gentleman</p>
      </div>
    </div>
  );
} 