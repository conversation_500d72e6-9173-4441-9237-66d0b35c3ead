import { gql } from 'graphql-request';

// Check if we're on the client side
const isClient = typeof window !== 'undefined';

// Product query using fragments for different product types
export const GET_PRODUCTS = gql`
  query GetProducts($first: Int, $after: String, $where: RootQueryToProductConnectionWhereArgs) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        price
        regularPrice
        salePrice
        onSale
        image {
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            sourceUrl
            altText
          }
        }
        ... on SimpleProduct {
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          stockStatus
          stockQuantity
          productCategories {
            nodes {
              name
              slug
            }
          }
        }
      }
    }
  }
`;

// Get a single product with variations if it's a variable product
export const GET_PRODUCT = gql`
  query GetProduct($id: ID!) {
    product(id: $id, idType: SLUG) {
      id
      databaseId
      name
      slug
      description
      shortDescription
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      image {
        sourceUrl
        altText
      }
      galleryImages {
        nodes {
          sourceUrl
          altText
        }
      }
      ... on SimpleProduct {
        stockStatus
        stockQuantity
      }
      ... on VariableProduct {
        attributes {
          nodes {
            name
            options
            variation
          }
        }
        defaultAttributes {
          nodes {
            name
            value
          }
        }
        variations {
          nodes {
            id
            databaseId
            name
            price
            regularPrice
            salePrice
            stockStatus
            stockQuantity
            attributes {
              nodes {
                name
                value
              }
            }
          }
        }
      }
    }
  }
`;

// Cart query without the totalShipping field that's causing errors
export const GET_CART = gql`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              name
              slug
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`;

// Add to cart mutation - Updated to match WooGraphQL documentation
export const ADD_TO_CART = gql`
  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {
    addToCart(
      input: {productId: $productId, variationId: $variationId, quantity: $quantity, extraData: $extraData}
    ) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
        contentsCount
      }
    }
  }
`;

// Update cart item quantities
export const UPDATE_CART_ITEM = gql`
  mutation UpdateCartItems($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                name
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
      }
    }
  }
`;

// Remove items from cart
export const REMOVE_ITEMS_FROM_CART = gql`
  mutation RemoveItemsFromCart($input: RemoveItemsFromCartInput!) {
    removeItemsFromCart(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                name
              }
            }
            quantity
          }
        }
        subtotal
        total
        isEmpty
      }
    }
  }
`;

/**
 * Fetch data from WooCommerce GraphQL API with caching and revalidation
 */
export async function fetchFromWooCommerce<T = Record<string, any>>(
  query: string,
  variables = {},
  tags: string[] = [],
  revalidate = 60
): Promise<T> {
  try {
    // Use different approaches for client and server
    if (isClient) {
      // When on client, use our proxy API route to avoid CORS issues
      const proxyEndpoint = '/api/graphql';

      // Build the fetch options
      const fetchOptions: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, variables }),
      };

      const response = await fetch(proxyEndpoint, fetchOptions);

      if (!response.ok) {
        throw new Error(`GraphQL API responded with status ${response.status}`);
      }

      const data = await response.json();

      if (data.errors) {
        console.error('GraphQL Errors:', data.errors);
        throw new Error(`GraphQL Error: ${data.errors[0]?.message || 'Unknown error'}`);
      }

      return data.data;
    } else {
      // Server-side: use direct GraphQL endpoint
      const endpoint = process.env.NEXT_PUBLIC_WORDPRESS_URL
        ? `${process.env.NEXT_PUBLIC_WORDPRESS_URL}/graphql`
        : '';

      if (!endpoint) {
        throw new Error('WordPress GraphQL endpoint not configured');
      }

      const fetchOptions: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query, variables }),
        next: {
          revalidate,
          tags
        },
      };

      const response = await fetch(endpoint, fetchOptions);

      if (!response.ok) {
        throw new Error(`GraphQL API responded with status ${response.status}`);
      }

      const data = await response.json();

      if (data.errors) {
        console.error('GraphQL Errors:', data.errors);
        throw new Error(`GraphQL Error: ${data.errors[0]?.message || 'Unknown error'}`);
      }

      return data.data;
    }
  } catch (error) {
    console.error('Error fetching from WooCommerce:', error);
    throw error;
  }
}

/**
 * GraphQL fetch with retry logic
 */
export async function wooGraphQLFetch<T = Record<string, any>>(
  query: string,
  variables = {},
  retries = 3,
  delay = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await fetchFromWooCommerce<T>(query, variables);
    } catch (error) {
      lastError = error as Error;
      console.error(`GraphQL request failed (attempt ${attempt}/${retries}):`, error);

      if (attempt < retries) {
        console.log(`Retrying request (${attempt}/${retries}) after ${delay}ms`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
      }
    }
  }

  console.error(`Failed after ${retries} attempts:`, lastError);
  throw lastError;
}

/**
 * Add items to cart - Updated for current WooGraphQL schema
 */
export async function addToCart(cartId: string, items: any[]) {
  try {
    interface AddToCartResponse {
      addToCart: {
        cart: {
          contents: {
            nodes: Array<{
              key: string;
              product: {
                node: {
                  id: string;
                  databaseId: number;
                  name: string;
                  slug: string;
                  image: {
                    sourceUrl: string;
                    altText: string;
                  };
                };
              };
              variation?: {
                node: {
                  id: string;
                  databaseId: number;
                  name: string;
                  attributes: {
                    nodes: Array<{
                      name: string;
                      value: string;
                    }>;
                  };
                };
              };
              quantity: number;
              total: string;
            }>;
          };
          subtotal: string;
          total: string;
          totalTax: string;
          isEmpty: boolean;
          contentsCount: number;
        };
      };
    }

    // Convert base64 encoded productId to integer databaseId
    const processedItems = items.map(item => {
      try {
        const databaseId = ensureDatabaseId(item.productId);
        return {
          productId: databaseId,
          quantity: item.quantity,
          variationId: item.variationId || undefined
        };
      } catch (error) {
        console.error('Error processing productId:', item.productId, error);
        throw new Error(`Invalid productId: ${item.productId}. ${error.message}`);
      }
    });

    // Use the first item for the mutation (WooGraphQL addToCart handles one item at a time)
    const firstItem = processedItems[0];

    const variables = {
      productId: firstItem.productId,
      quantity: firstItem.quantity,
      variationId: firstItem.variationId || null,
      extraData: null
    };

    console.log('Adding to cart with variables:', variables);

    const response = await wooGraphQLFetch<AddToCartResponse>(
      ADD_TO_CART.loc?.source?.body || ADD_TO_CART.toString(),
      variables
    );

    console.log('Add to cart response:', response);
    return response.addToCart.cart;
  } catch (error) {
    console.error('Error adding items to cart:', error);
    throw error;
  }
}

/**
 * Get all products from WooCommerce
 */
export async function getAllProducts(first = 50, after?: string) {
  try {
    const data = await fetchFromWooCommerce(
      GET_PRODUCTS.loc?.source?.body || GET_PRODUCTS.toString(),
      { first, after }
    );
    return data?.products?.nodes || [];
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

/**
 * Create a new cart
 */
export async function createCart() {
  try {
    // WooGraphQL automatically creates a cart when needed
    // We just need to fetch the current cart state
    const data = await fetchFromWooCommerce(
      GET_CART.loc?.source?.body || GET_CART.toString()
    );
    return data?.cart || null;
  } catch (error) {
    console.error('Error creating cart:', error);
    throw error;
  }
}

/**
 * Get current cart
 */
export async function getCart() {
  try {
    const data = await fetchFromWooCommerce(
      GET_CART.loc?.source?.body || GET_CART.toString()
    );
    return data?.cart || null;
  } catch (error) {
    console.error('Error fetching cart:', error);
    throw error;
  }
}

/**
 * Update cart item quantities
 */
export async function updateCart(items: Array<{ key: string; quantity: number }>) {
  try {
    const input = {
      clientMutationId: `update_cart_${Date.now()}`,
      items
    };

    const data = await fetchFromWooCommerce(
      UPDATE_CART_ITEM.loc?.source?.body || UPDATE_CART_ITEM.toString(),
      { input }
    );
    return data?.updateItemQuantities?.cart || null;
  } catch (error) {
    console.error('Error updating cart:', error);
    throw error;
  }
}

/**
 * Remove items from cart
 */
export async function removeFromCart(keys: string[]) {
  try {
    const input = {
      clientMutationId: `remove_from_cart_${Date.now()}`,
      keys
    };

    const data = await fetchFromWooCommerce(
      REMOVE_ITEMS_FROM_CART.loc?.source?.body || REMOVE_ITEMS_FROM_CART.toString(),
      { input }
    );
    return data?.removeItemsFromCart?.cart || null;
  } catch (error) {
    console.error('Error removing from cart:', error);
    throw error;
  }
}

/**
 * Normalize cart data to match existing frontend structure
 */
export function normalizeCart(cart: any) {
  if (!cart) return null;

  return {
    id: cart.id || 'default',
    items: cart.contents?.nodes?.map((item: any) => ({
      id: item.key,
      productId: item.product?.node?.databaseId?.toString() || item.product?.node?.id,
      quantity: item.quantity,
      name: item.product?.node?.name || '',
      price: item.total || '0',
      image: {
        url: item.product?.node?.image?.sourceUrl || '',
        altText: item.product?.node?.image?.altText || ''
      }
    })) || [],
    itemCount: cart.contents?.nodes?.length || 0,
    subtotal: cart.subtotal || '0',
    total: cart.total || '0'
  };
}

/**
 * Normalize product data to match the existing frontend structure
 * This helps maintain compatibility with the existing components
 * IMPORTANT: Uses databaseId for cart operations instead of base64 encoded id
 */
export function normalizeProduct(product: any) {
  if (!product) return null;

  // Extract product type
  const isVariable = Boolean(product.variations?.nodes?.length);

  // Extract pricing data
  let priceRange = {
    minVariantPrice: {
      amount: product.price || "0",
      currencyCode: "INR" // Default currency for the application
    },
    maxVariantPrice: {
      amount: product.price || "0",
      currencyCode: "INR"
    }
  };

  // Extract options for variable products
  let options: any[] = [];
  if (isVariable && product.attributes?.nodes) {
    options = product.attributes.nodes
      .filter((attr: any) => attr.variation)
      .map((attr: any) => ({
        id: attr.id,
        name: attr.name,
        values: attr.options || []
      }));
  }

  // Extract variants for variable products
  let variants: any[] = [];
  if (isVariable && product.variations?.nodes) {
    variants = product.variations.nodes.map((variation: any) => ({
      id: variation.databaseId?.toString() || variation.id, // Use databaseId for cart operations
      title: variation.name,
      price: {
        amount: variation.price || "0",
        currencyCode: "INR"
      },
      compareAtPrice: variation.regularPrice !== variation.price ? {
        amount: variation.regularPrice || "0",
        currencyCode: "INR"
      } : null,
      availableForSale: variation.stockStatus === 'IN_STOCK',
      selectedOptions: variation.attributes?.nodes?.map((attr: any) => ({
        name: attr.name,
        value: attr.value
      })) || []
    }));
  }

  // Extract images
  let images: any[] = [];
  if (product.image) {
    images.push({
      url: product.image.sourceUrl,
      altText: product.image.altText || product.name
    });
  }
  if (product.galleryImages?.nodes) {
    const galleryImages = product.galleryImages.nodes.map((img: any) => ({
      url: img.sourceUrl,
      altText: img.altText || product.name
    }));
    images = [...images, ...galleryImages];
  }

  // Extract collections/categories
  let collections: any[] = [];
  if (product.productCategories?.nodes) {
    collections = product.productCategories.nodes.map((cat: any) => ({
      id: cat.id,
      handle: cat.slug,
      title: cat.name
    }));
  }

  // Extract metafields (if any)
  let metafields: any[] = [];

  // Return normalized product object that matches existing frontend structure
  // CRITICAL: Use databaseId as the main id for cart operations
  return {
    id: product.databaseId?.toString() || product.id, // Use databaseId for cart compatibility
    handle: product.slug,
    title: product.name,
    description: product.description || '',
    descriptionHtml: product.description || '',
    priceRange,
    options,
    variants,
    images,
    collections,
    availableForSale: product.stockStatus !== 'OUT_OF_STOCK',
    metafields,
    // Add original data for reference if needed
    _originalWooProduct: product
  };
}

/**
 * Utility function to convert base64 encoded GraphQL ID to database ID
 * WooCommerce GraphQL uses base64 encoded IDs like "cG9zdDo0Mg=="
 * which decode to "post:42" where 42 is the database ID
 */
export function extractDatabaseIdFromBase64(base64Id: string): number | null {
  try {
    if (!base64Id || typeof base64Id !== 'string') {
      return null;
    }

    // If it's already a number string, return it
    if (base64Id.match(/^\d+$/)) {
      return parseInt(base64Id, 10);
    }

    // Try to decode base64
    const decoded = atob(base64Id);
    const match = decoded.match(/^post:(\d+)$/);

    if (match && match[1]) {
      return parseInt(match[1], 10);
    }

    return null;
  } catch (error) {
    console.error('Error extracting database ID from base64:', error);
    return null;
  }
}

/**
 * Utility function to ensure we have a valid database ID for cart operations
 */
export function ensureDatabaseId(id: string | number): number {
  if (typeof id === 'number') {
    return id;
  }

  if (typeof id === 'string') {
    // If it's already a number string
    if (id.match(/^\d+$/)) {
      return parseInt(id, 10);
    }

    // Try to extract from base64
    const databaseId = extractDatabaseIdFromBase64(id);
    if (databaseId !== null) {
      return databaseId;
    }
  }

  throw new Error(`Cannot convert ID "${id}" to database ID. Expected integer or valid base64 encoded ID.`);
}