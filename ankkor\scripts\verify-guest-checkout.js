/**
 * <PERSON><PERSON><PERSON> to verify WooCommerce guest checkout settings
 * 
 * This script will check if guest checkout is properly enabled in your WooCommerce settings
 * and provide guidance on how to fix it if it's not.
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config();

// Get WooCommerce URL from environment variables
const wooCommerceUrl = process.env.NEXT_PUBLIC_WOOCOMMERCE_URL || process.env.NEXT_PUBLIC_BACKEND_URL;

if (!wooCommerceUrl) {
  console.error('❌ Error: WooCommerce URL not found in environment variables.');
  console.log('Please set NEXT_PUBLIC_WOOCOMMERCE_URL or NEXT_PUBLIC_BACKEND_URL in your .env.local file.');
  process.exit(1);
}

console.log(`🔍 Verifying guest checkout settings for: ${wooCommerceUrl}`);

// Function to make an HTTP request
const makeRequest = (url) => {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    
    protocol.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({ 
          statusCode: res.statusCode,
          data,
          headers: res.headers
        });
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
};

// Main function to check guest checkout
const checkGuestCheckout = async () => {
  try {
    // Try to access the checkout page with guest checkout parameters
    const testUrl = `${wooCommerceUrl}/checkout/?guest_checkout=yes`;
    console.log(`Testing checkout URL: ${testUrl}`);
    
    const response = await makeRequest(testUrl);
    
    // Check if we're redirected to login
    const isRedirectedToLogin = 
      response.data.includes('login') && 
      response.data.includes('password') && 
      !response.data.includes('checkout-guest');
    
    if (isRedirectedToLogin) {
      console.error('❌ Guest checkout appears to be disabled in your WooCommerce settings.');
      console.log('\nTo enable guest checkout:');
      console.log('1. Log in to your WordPress admin dashboard');
      console.log('2. Go to WooCommerce > Settings > Accounts & Privacy');
      console.log('3. Check the box for "Allow customers to place orders without an account"');
      console.log('4. Click "Save changes" at the bottom of the page');
    } else {
      console.log('✅ Guest checkout appears to be enabled!');
    }
    
    // Additional guidance
    console.log('\n📝 Additional steps to ensure guest checkout works:');
    console.log('1. Make sure your theme supports guest checkout');
    console.log('2. Check if any plugins might be overriding the guest checkout settings');
    console.log('3. Test the checkout flow manually to verify it works end-to-end');
    
  } catch (error) {
    console.error('❌ Error checking guest checkout:', error.message);
  }
};

// Run the check
checkGuestCheckout(); 